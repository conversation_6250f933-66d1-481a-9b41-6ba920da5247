'use client'
import React, { useEffect, useState } from 'react'
import SearchIcon from '../icons/SearchIcon';
interface Prop {
    debounce?: number;
    onChange: (payload: string) => void;
    value: string;
    className?: string;
    placeholder?: string
}
const DebounceInput = ({ value: initialValue, placeholder = 'Search for product', debounce = 500, onChange, className, ...props }: Prop) => {

    const [value, setValue] = useState(initialValue)
    useEffect(() => {
        setValue(initialValue)

    }, [initialValue])
    useEffect(() => {
        const timeout = setTimeout(() => {
            onChange(value)

        }, debounce)

        return () => clearTimeout(timeout)
    }, [value, debounce, onChange])


    return (
        <div className={`${className}  w-full border-[.2992px] border-[#898989] rounded-md items-center px-3 py-[14px] divide-x-2`}>
            <input
                className='outline-none w-full border-none bg-transparent text-[#B2B5BE] pr-2 text-xs'
                onChange={(e) => setValue(e.target.value)}
                {...props}
                placeholder={placeholder}
                type="text"
                value={value}
            />
            <div className="px-2 cursor-pointer">
                <SearchIcon />
            </div>


        </div>
    )
}

export default DebounceInput