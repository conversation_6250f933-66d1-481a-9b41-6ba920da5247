"use client"
import Image from 'next/image'
import React, { useEffect, useState } from 'react'
import SearchIcon from '../icons/SearchIcon'
import BasketIcon from '../icons/BasketIcon'
import { Button } from '@/components/core'
import { Headerimages } from '@/app/(web-store)/[webStoreId]/components/api/store/fetchStore'
import DebounceInput from '../util/DebounceInput'
import CartSummary from '../cart/CartSummary'
import useCartStore from '@/app/(web-store)/[webStoreId]/components/store/cartStore'
import CartFullScreen from '../cart/CartFullScreen'
// import { Button } from '@/components/core'
interface Prop {
    header_images: Headerimages;
    header_logo_text: string;
    header_logos: Headerimages;
    header_description: string;
    globalFilter: string;
    setGlobalFilter: React.Dispatch<React.SetStateAction<string>>;
    storeId: string;
    setShowSuccessModal: React.Dispatch<React.SetStateAction<boolean>>
    setOrderId: React.Dispatch<React.SetStateAction<string>>
    CheckoutDetails: {
        first_nmae: string;
        last_name: string;
        phone_number: string;
    }
    setCheckoutDetails: React.Dispatch<React.SetStateAction<{
        first_nmae: string;
        last_name: string;
        phone_number: string;
    }>>
    branchId: string;
    companyId: string
    showCartFullScreen: boolean;
    setShowCartFullScreen: React.Dispatch<React.SetStateAction<boolean>>
}
const Header = ({ header_logo_text, header_description, setShowCartFullScreen, showCartFullScreen, header_logos, globalFilter, branchId, companyId, setGlobalFilter, storeId, setCheckoutDetails, setShowSuccessModal, setOrderId, CheckoutDetails }: Prop) => {
    const [showCart, setShowCart] = useState(false)
    // const [showCartFullScreen, setShowCartFullScreen] = useState(false)
    const cart = useCartStore((state) => state.carts[storeId]);
    const [data, setData] = useState(cart);
    useEffect(() => {
        if (cart?.length > 0) {
            setShowCart(true)
        } else {
            setShowCart(false)
        }
    }, [cart])

    return (
        <div className="">
            <div className=' w-full px-4 bg-white sticky top-0 z-[99999] flex justify-between items-center py-3 md:py-7 sm:px-[2rem] lg:px-[5.75rem]'>
                <div className="flex gap-[.7738rem]">
                    <div className="">
                        <Image
                            alt='logo'
                            height={50}
                            src={header_logos?.url}
                            width={50}
                        />

                    </div>
                    <div className=""><h2 className='font-semibold text-base md:text-[1.375rem] font-outfit'>{header_logo_text}</h2>
                        <p className='text-[#4E4E4E] text-xxs md:text-xs font-sans'>{header_description}</p></div>
                </div>
                <div className="flex items-center gap-5">

                    <DebounceInput
                        className='md:flex hidden'
                        value={globalFilter ?? ""}
                        onChange={(value) => setGlobalFilter(String(value))}
                    />
                    <div className=" w-[2.625rem] h-[2.625rem] rounded-full bg-[#F8F8F8] flex md:hidden justify-center items-center">
                        <Button className='bg-transparent'>   <SearchIcon fill='#000' /></Button>
                    </div>
                    <div className="w-[2.625rem] h-[2.625rem] rounded-full bg-[#F8F8F8] flex justify-center items-center">
                        <Button className='bg-transparent' onClick={() => setShowCart(!showCart)}>  <BasketIcon /></Button>
                    </div>
                </div>

                {showCart && <div className="bg-white shadow-sm fixed top-[9vh] sm:top-[11vh] rounded-10 rounded-t-none z-[*********] overflow-hidden right-0 w-[25rem] md:w-[29rem] min-h-[5rem] max-h-[30rem] sm:max-h-[45rem]"

                ><CartSummary data={data} setOpen={setShowCart} setOrderId={setOrderId} setShowCartFullScreen={setShowCartFullScreen} setShowSuccessModal={setShowSuccessModal} storeId={storeId} /></div>}


            </div>
            {showCartFullScreen && <CartFullScreen branchId={branchId} CheckoutDetail={CheckoutDetails} companyId={companyId} open={showCartFullScreen} setCheckoutDetails={setCheckoutDetails} setData={setData} setOpen={setShowCartFullScreen} setOrderId={setOrderId} setShowCart={setShowCart} setShowSuccessModal={setShowSuccessModal} storeId={storeId} />}
        </div>
    )
}

export default Header