export interface ProductDetaisTypeProp {
    status: string;
    status_code: number;
    data: ProductDetails;
    errors: null;
}

export interface ProductDetails {
    message: string;
    items: Item[];
    count: number;
    total_items: number;
}

export interface Item {
    name: string;
    product_description: string;
    product_tag: string;
    selling_price: string;
    images: string[];
}


