"use clientSSS"
import { Category } from '@/app/(web-store)/[webStoreId]/components/header-section/WebStoreHeader';
import { Button, Command, CommandItem, CommandList, } from '@/components/core';
import React, { useEffect, useState } from 'react'
import GridIcon from '../icons/GridIcon';
import * as <PERSON><PERSON><PERSON> from "@radix-ui/react-menubar";
import DebounceInput from '../util/DebounceInput';
import CaretDownIcon from '../icons/CaretDown';
import { useSearchParams } from 'next/navigation';
interface prop {
    navigation_alignment: string;
    navigation_set_menu: Category[];
    navigation_visible: boolean;
    activeTab: string;
    setActiveTab: React.Dispatch<React.SetStateAction<string>>;
    setGlobalFilter: React.Dispatch<React.SetStateAction<string>>;
}
const CategoryList = ({ navigation_set_menu, activeTab, setActiveTab, setGlobalFilter, }: prop) => {
    // const [activeTab, setActiveTab] = useState("All products")
    const [searchFIlter, setSearchFIlter] = useState("")
    const searchParams = useSearchParams();

    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [filteredCategories, setFilteredCategories] = useState<Category[]>(navigation_set_menu);
    const filteredItems = filteredCategories?.filter((item) =>
        item?.name?.toLowerCase()?.includes(searchFIlter.toLowerCase())
    );
    // const [seletedTab, setSeletedTab] = useState(filteredCategories[0]?.id ?? "");

    const [isHasCategory, setIsHasCategory] = useState(false)

    useEffect(() => {
        // Get the 'category' query parameters from the URL
        const categoryIds = searchParams.getAll('category');

        // Check if there are categories in the URL params
        if (categoryIds.length > 0) {
            setIsHasCategory(true)
            // setHasCategoryInParams(true); // Set to true if categories exist in params

            // Filter the categories based on the URL params
            const filtered = navigation_set_menu.filter((category) =>
                categoryIds.includes(category.id)
            );
            setActiveTab(filtered[0]?.id)
            setFilteredCategories(filtered);
        } else {
            // setHasCategoryInParams(false); // No categories in params, show all categories
            setFilteredCategories(navigation_set_menu);
        }
    }, [navigation_set_menu, searchParams, setActiveTab]);


    return (
        <div >
            <div className="gap-2 sm:gap-x-[.5rem] flex items-center mt-3 sticky top-4">
                {!isHasCategory && <Button className='px-4 w-[10rem] py-[.875rem] bg-black font-bold rounded-[.8125rem]'
                    style={{
                        backgroundColor: activeTab === "" ? "black" : "white",
                        color: activeTab === "" ? "white" : "black",
                    }}
                    onClick={() => {
                        setActiveTab("")
                        setGlobalFilter("")
                    }}
                >
                    All products
                </Button>}
                <div className="hidden md:flex items-center justify-between w-full overflow-x-hidden">

                    <div className="space-x-[.5rem] hidden md:flex ">
                        {filteredCategories?.slice(0, 15)?.map((nav) => (
                            <Button className=' px-4  h-[2.9375rem] text-black truncate text-opacity-100 font-bold rounded-[.8125rem]' key={nav?.id} style={{
                                backgroundColor: activeTab === nav?.id ? "black" : "white",
                                color: activeTab === nav?.id ? "white" : "black",
                            }}
                                onClick={() => {
                                    setActiveTab(nav?.id)
                                    setGlobalFilter("")
                                }}

                            >
                                {nav?.name}
                            </Button>

                        ))
                        }

                    </div>



                </div>
                {filteredCategories?.length > 6 && <Menubar.Root className="hidden md:block py-3">
                    <Menubar.Menu>
                        <Menubar.Trigger onClick={() => setIsMenuOpen(!isMenuOpen)}>
                            <Button
                                className="flex justify-center items-center h-[2.9375rem] w-[4.25rem] bg-black font-bold rounded-[.8125rem]"
                                style={{
                                    backgroundColor: "black",
                                    color: activeTab === "" ? "white" : "black",
                                }}
                                onClick={() => {
                                    setActiveTab("");
                                    setGlobalFilter("");
                                    setSearchFIlter("");
                                }}
                            >
                                <GridIcon />
                            </Button>
                        </Menubar.Trigger>
                        {isMenuOpen && (
                            <Menubar.Portal>
                                <Menubar.Content
                                    align="start"
                                    alignOffset={-14}
                                    className="min-w-[220px] hidden md:block z-[9999999999] rounded-md bg-white p-[5px] shadow-[0px_10px_38px_-10px_rgba(22,_23,_24,_0.35),_0px_10px_20px_-15px_rgba(22,_23,_24,_0.2)] will-change-[transform,opacity] [animation-duration:_400ms] [animation-timing-function:_cubic-bezier(0.16,_1,_0.3,_1)]"
                                    sideOffset={5}
                                >
                                    <div className="overflow-y-auto h-[30rem]">
                                        <Command className="relative">
                                            <DebounceInput
                                                className="bg-white sticky top-0 py-3 border-[#e5e5e5] border-[0.5px] mb-2 rounded-md flex"
                                                value={searchFIlter ?? ""}
                                                onChange={(value) => setSearchFIlter(String(value))}
                                            />
                                            <CommandList className="max-h-[50vh] overflow-y-auto scrollbar-thin !scrollbar-thumb-gray-400 !scrollbar-track-red-900">
                                                {filteredItems?.map((item) => (
                                                    <CommandItem
                                                        className="hover:bg-gray-200 hover:py-2 text-sm font-outfit"
                                                        key={item?.id}
                                                        onSelect={() => {
                                                            setActiveTab(item?.id);
                                                            setIsMenuOpen(false); // Close after selecting an item
                                                        }}
                                                    >
                                                        {item?.name}
                                                    </CommandItem>
                                                ))}
                                            </CommandList>
                                        </Command>

                                        {/* Close button */}

                                    </div>
                                </Menubar.Content>
                            </Menubar.Portal>
                        )}
                    </Menubar.Menu>
                </Menubar.Root>}

                <div className="md:hidden w-full">



                    <Menubar.Root className="md:hidden sm:px-6">
                        <Menubar.Menu>
                            <Menubar.Trigger className="w-full" onClick={() => setIsMenuOpen(!isMenuOpen)}>
                                <div className="w-full flex justify-between px-4 py-[.375rem] rounded-[.4375rem] bg-white items-center h-[2.75rem] border-[0.5px] text-sm text-left">
                                    {"View more categories"}
                                    <button className="w-[2.3125rem] hover:opacity-80 h-[2rem] bg-black rounded-[.4375rem] flex justify-center items-center">
                                        <CaretDownIcon />
                                    </button>
                                </div>
                            </Menubar.Trigger>
                            {isMenuOpen && (
                                <Menubar.Portal>
                                    <Menubar.Content
                                        align="end"
                                        alignOffset={-14}
                                        className="w-full z-[9999999999] md:hidden rounded-md bg-white p-[5px] shadow-[0px_10px_38px_-10px_rgba(22,_23,_24,_0.35),_0px_10px_20px_-15px_rgba(22,_23,_24,_0.2)] will-change-[transform,opacity] [animation-duration:_400ms] [animation-timing-function:_cubic-bezier(0.16,_1,_0.3,_1)]"
                                        sideOffset={5}
                                    >
                                        <div className="overflow-y-auto h-[30rem]">
                                            <Command className="relative">
                                                <DebounceInput
                                                    className="bg-white sticky top-0 py-3 border-[#e5e5e5] border-[0.5px] mb-2 rounded-md flex"
                                                    value={searchFIlter ?? ""}
                                                    onChange={(value) => setSearchFIlter(String(value))}
                                                />
                                                <CommandList className="max-h-[50vh] overflow-y-auto scrollbar-thin !scrollbar-thumb-gray-400 !scrollbar-track-red-900">
                                                    {filteredItems?.map((item) => (
                                                        <CommandItem
                                                            className="hover:bg-gray-200 hover:py-2 text-sm font-outfit"
                                                            key={item?.id}
                                                            onSelect={() => {
                                                                setActiveTab(item?.id);
                                                                setIsMenuOpen(false); // Close menu after selection
                                                            }}
                                                        >
                                                            {item?.name}
                                                        </CommandItem>
                                                    ))}
                                                </CommandList>
                                            </Command>


                                        </div>
                                    </Menubar.Content>
                                </Menubar.Portal>
                            )}
                        </Menubar.Menu>
                    </Menubar.Root>


                </div>
            </div>
        </div>
    )
}

export default CategoryList

