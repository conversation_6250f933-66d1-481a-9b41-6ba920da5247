import * as React from "react";
import { SVGProps } from "react";

interface CardIconProps extends SVGProps<SVGSVGElement> {
    color?: string; // Optional prop for customizing the color
    size?: number;  // Optional prop for customizing the size
}

const CardIcon: React.FC<CardIconProps> = ({ color = "#292D32", size = 20, ...props }) => (
    <svg
        fill="none"
        height={size}
        viewBox="0 0 20 20"
        width={size}
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path
            d="M13.81 17.047H6.192c-2.78 0-4.38-1.6-4.38-4.38V7.332c0-2.78 1.6-4.38 4.38-4.38h7.62c2.78 0 4.38 1.6 4.38 4.38v5.333c0 2.781-1.6 4.381-4.38 4.381M6.192 4.095c-2.179 0-3.238 1.059-3.238 3.238v5.333c0 2.18 1.06 3.239 3.238 3.239h7.62c2.179 0 3.238-1.06 3.238-3.239V7.333c0-2.179-1.06-3.238-3.238-3.238z"
            fill={color}
        />
        <path
            d="M10 12.857a2.858 2.858 0 0 1 0-5.714 2.858 2.858 0 0 1 0 5.714m0-4.572c-.945 0-1.715.77-1.715 1.715 0 .944.77 1.714 1.715 1.714.944 0 1.714-.77 1.714-1.714 0-.945-.77-1.715-1.714-1.715m-4.952 4.191a.576.576 0 0 1-.571-.572v-3.81c0-.312.259-.57.571-.57s.571.258.571.57v3.81a.576.576 0 0 1-.571.572m9.904 0a.576.576 0 0 1-.571-.572v-3.81c0-.312.259-.57.571-.57.313 0 .572.258.572.57v3.81a.576.576 0 0 1-.572.572"
            fill={color}
        />
    </svg>
);

export default CardIcon;
