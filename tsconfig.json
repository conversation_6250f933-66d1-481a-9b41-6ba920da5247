{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/app/(dashboard)/spend-management/misc/components/ExpenseListDialog.tsx", "src/app/(dashboard)/cards/request-card/pick-up/physical-card/update-address/[id]e-address/[id]/[id]", "src/app/(dashboard)/send-money/misc/components/ConnectBankAccountButton.jsx", "src/app/(dashboard)/payroll/employee-time-attendance/[companyid]/[companyName]/[name]/components/test", "src/app/(dashboard)/sales/create-sales", "src/app/(dashboard)/instant-web/misc/components/SelectBranchQtsx"], "exclude": ["node_modules"]}