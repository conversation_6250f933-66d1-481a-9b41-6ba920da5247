import * as React from "react";
import { SVGProps } from "react";

const LoadMoreicon = (props: SVGProps<SVGSVGElement>) => (
    <div
        style={{
            padding: "10px", // Add padding around the SVG
            backgroundColor: "#f0f0f0", // Optional: background color to show the padding clearly
            display: "inline-block", // Keeps the padding visible without extra space
        }}
    >
        <svg
            fill="none"
            height={20}
            viewBox="0 0 42 10"
            width={42}
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <style>
                {`
        @keyframes bounce {
          0%, 100% {
            transform: translateY(0);
          }
          50% {
            transform: translateY(-5px);
          }
        }
        
        .circle {
          animation: bounce 0.6s infinite ease-in-out;
        }
        
        .circle:nth-child(1) {
          animation-delay: 0s;
        }
        
        .circle:nth-child(2) {
          animation-delay: 0.2s;
        }
        
        .circle:nth-child(3) {
          animation-delay: 0.4s;
        }
      `}
            </style>
            <circle className="circle" cx={5} cy={5} fill="#EF4444" r={5} />
            <circle className="circle" cx={21} cy={5} fill="#00A37D" r={5} />
            <circle className="circle" cx={37} cy={5} fill="#D68F1C" r={5} />
        </svg>
    </div>
);

export default LoadMoreicon;
