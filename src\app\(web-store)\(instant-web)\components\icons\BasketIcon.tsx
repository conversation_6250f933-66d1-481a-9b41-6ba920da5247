import * as React from "react";
import { SVGProps } from "react";

interface BasketIconProps extends SVGProps<SVGSVGElement> {
    color?: string; // Add a color prop to make the color reusable
}

const BasketIcon = ({ color = "black", ...props }: BasketIconProps) => (
    <svg
        fill="none"
        height={20}
        viewBox="0 0 20 20"
        width={20}
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path
            d="M4.32409 5.31686C4.16576 5.31686 3.99909 5.2502 3.88242 5.13353C3.64076 4.89186 3.64076 4.49186 3.88242 4.2502L6.90742 1.2252C7.14909 0.983529 7.54909 0.983529 7.79076 1.2252C8.03242 1.46686 8.03242 1.86686 7.79076 2.10853L4.76576 5.13353C4.64076 5.2502 4.48242 5.31686 4.32409 5.31686Z"
            fill={color} // Use color prop
        />
        <path
            d="M15.6753 5.31686C15.5169 5.31686 15.3586 5.25853 15.2336 5.13353L12.2086 2.10853C11.9669 1.86686 11.9669 1.46686 12.2086 1.2252C12.4503 0.983529 12.8503 0.983529 13.0919 1.2252L16.1169 4.2502C16.3586 4.49186 16.3586 4.89186 16.1169 5.13353C16.0003 5.2502 15.8336 5.31686 15.6753 5.31686Z"
            fill={color} // Use color prop
        />
        <path
            d="M16.841 8.83333C16.7827 8.83333 16.7243 8.83333 16.666 8.83333H16.4743H3.33268C2.74935 8.84167 2.08268 8.84167 1.59935 8.35833C1.21602 7.98333 1.04102 7.4 1.04102 6.54167C1.04102 4.25 2.71602 4.25 3.51602 4.25H16.4827C17.2827 4.25 18.9577 4.25 18.9577 6.54167C18.9577 7.40833 18.7827 7.98333 18.3993 8.35833C17.966 8.79167 17.3827 8.83333 16.841 8.83333ZM3.51602 7.58333H16.6743C17.0493 7.59167 17.3993 7.59167 17.516 7.475C17.5743 7.41667 17.6993 7.21667 17.6993 6.54167C17.6993 5.6 17.466 5.5 16.4743 5.5H3.51602C2.52435 5.5 2.29102 5.6 2.29102 6.54167C2.29102 7.21667 2.42435 7.41667 2.47435 7.475C2.59102 7.58333 2.94935 7.58333 3.31602 7.58333H3.51602Z"
            fill={color} // Use color prop
        />
        <path
            d="M8.13281 15.2503C7.79115 15.2503 7.50781 14.967 7.50781 14.6253V11.667C7.50781 11.3253 7.79115 11.042 8.13281 11.042C8.47448 11.042 8.75781 11.3253 8.75781 11.667V14.6253C8.75781 14.9753 8.47448 15.2503 8.13281 15.2503Z"
            fill={color} // Use color prop
        />
        <path
            d="M11.9668 15.2503C11.6251 15.2503 11.3418 14.967 11.3418 14.6253V11.667C11.3418 11.3253 11.6251 11.042 11.9668 11.042C12.3085 11.042 12.5918 11.3253 12.5918 11.667V14.6253C12.5918 14.9753 12.3085 15.2503 11.9668 15.2503Z"
            fill={color} // Use color prop
        />
        <path
            d="M12.4083 18.9586H7.38329C4.39996 18.9586 3.73329 17.1836 3.47496 15.642L2.29996 8.43362C2.24162 8.09195 2.47496 7.77528 2.81662 7.71695C3.15829 7.65862 3.47496 7.89195 3.53329 8.23362L4.70829 15.4336C4.94996 16.9086 5.44996 17.7086 7.38329 17.7086H12.4083C14.55 17.7086 14.7916 16.9586 15.0666 15.5086L16.4666 8.21695C16.5333 7.87528 16.8583 7.65028 17.2 7.72528C17.5416 7.79195 17.7583 8.11695 17.6916 8.45862L16.2916 15.7503C15.9666 17.4419 15.425 18.9586 12.4083 18.9586Z"
            fill={color} // Use color prop
        />
    </svg>
);

export default BasketIcon;
