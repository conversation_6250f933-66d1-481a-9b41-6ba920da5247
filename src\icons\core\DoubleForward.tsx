import * as React from "react";
import { SVGProps } from "react";
const DoubleForward = (props: SVGProps<SVGSVGElement>) => (
  <svg
    fill={props.fill || "#000000"}
    height={20}
    viewBox="0 0 25 20"
    width={25}
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      clipRule="evenodd"
      d="M16.5217 7.64342C17.7801 8.90178 17.822 10.9159 16.6476 12.2247L16.5217 12.3575L13.0889 15.5897C12.7634 15.9151 12.2358 15.9151 11.9104 15.5897C11.61 15.2893 11.5868 14.8166 11.841 14.4897L11.9104 14.4112L15.3432 11.179C15.9598 10.5623 15.9923 9.58277 15.4406 8.92795L15.3432 8.82194L11.9104 5.5897C11.5849 5.26427 11.5849 4.73663 11.9104 4.41119C12.2108 4.11079 12.6835 4.08768 13.0104 4.34187L13.0889 4.41119L16.5217 7.64342Z"
      fill={props.fill || "#000000"}
      fillRule="evenodd"
    />
    <g opacity={0.3}>
      <path
        clipRule="evenodd"
        d="M11.5217 7.64342C12.7801 8.90178 12.822 10.9159 11.6476 12.2247L11.5217 12.3575L8.08887 15.5897C7.76343 15.9151 7.23579 15.9151 6.91036 15.5897C6.60995 15.2893 6.58685 14.8166 6.84103 14.4897L6.91036 14.4112L10.3432 11.179C10.9598 10.5623 10.9923 9.58277 10.4406 8.92795L10.3432 8.82194L6.91036 5.5897C6.58492 5.26427 6.58492 4.73663 6.91036 4.41119C7.21076 4.11079 7.68345 4.08768 8.01036 4.34187L8.08887 4.41119L11.5217 7.64342Z"
        fill={props.fill || "#000000"}
        fillRule="evenodd"
      />
    </g>
  </svg>
);
export default DoubleForward;
