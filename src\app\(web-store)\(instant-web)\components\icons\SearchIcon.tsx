import * as React from "react";
import { SVGProps } from "react";

const SearchIcon = (props: SVGProps<SVGSVGElement>) => (
    <svg
        fill="none"
        height={14}
        viewBox="0 0 14 14"
        width={14}
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path
            d="M6.70768 12.6878C3.41185 12.6878 0.728516 10.0045 0.728516 6.70866C0.728516 3.41283 3.41185 0.729492 6.70768 0.729492C10.0035 0.729492 12.6868 3.41283 12.6868 6.70866C12.6868 10.0045 10.0035 12.6878 6.70768 12.6878ZM6.70768 1.60449C3.89018 1.60449 1.60352 3.89699 1.60352 6.70866C1.60352 9.52033 3.89018 11.8128 6.70768 11.8128C9.52518 11.8128 11.8118 9.52033 11.8118 6.70866C11.8118 3.89699 9.52518 1.60449 6.70768 1.60449Z"
            fill={props.fill || "#B2B5BE"} // Dynamic color with default fallback
        />
        <path
            d="M12.8332 13.2707C12.7223 13.2707 12.6115 13.2298 12.524 13.1423L11.3573 11.9757C11.1882 11.8065 11.1882 11.5265 11.3573 11.3573C11.5265 11.1882 11.8065 11.1882 11.9757 11.3573L13.1423 12.524C13.3115 12.6932 13.3115 12.9732 13.1423 13.1423C13.0548 13.2298 12.944 13.2707 12.8332 13.2707Z"
            fill={props.fill || "#B2B5BE"} // Dynamic color with default fallback
        />
    </svg>
);

export default SearchIcon;
