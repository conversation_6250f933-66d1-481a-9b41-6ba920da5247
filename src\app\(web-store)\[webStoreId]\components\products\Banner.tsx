'use client'
// import Image from 'next/image'
import React from 'react'
interface Prop {
    bannerUrl: string
}
const Banner = ({ bannerUrl }: Prop) => {
    return (
        // <div className='hidden md:block w-full h-[10.75rem] md:h-[14.75rem] mt-[.0313rem] '>
        //     {/*  eslint-disable-next-line @next/next/no-img-element */}
        //     <img alt="store banner img" className='rounded-t-10 shrink-0' src={bannerUrl} style={{ width: "100%", height: "100%", objectFit: "cover" }} />
        // </div>

        <div
            aria-label="store banner img"
            className='hidden md:block w-full h-[10.75rem] md:h-[14.75rem] mt-[.0313rem] rounded-t-10'
            style={{
                backgroundImage: `url(${bannerUrl})`,
                backgroundSize: 'cover',
                // backgroundPosition: 'top center',
                backgroundRepeat: 'no-repeat'
            }}
        ></div>

    )
}

export default Banner       