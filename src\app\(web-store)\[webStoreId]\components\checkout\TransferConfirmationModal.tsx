"use client"
import React from 'react';
import * as Dialog from '@radix-ui/react-dialog';

import { Button } from '@/components/core';
// import { DialogHeader } from '@/components/core';


interface Prop {
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    open: boolean;
    message: string;
}
const TranserConfirmModal = ({ open, setOpen, message }: Prop) => {

    return (
        <div className='bg-yellow-900 w-full'>
            <Dialog.Root open={open}>
                <Dialog.Portal>
                    <Dialog.Overlay className="fixed  inset-0 z-[9999999999999999] bg-[#efefef] backdrop-blur-2xl  transition-all duration-100 data-[state=closed]:animate-out data-[state=closed]:fade-out data-[state=open]:fade-in" />
                    <h2>in</h2>
                    <Dialog.Content className="w-[38.8125rem] z-[9999999999999999999999999999] max-w-[98%] lg:max-w-[71.4375rem] fixed left-[50%] max-md:bottom-0  md:top-[50%] animate-in md:translate-y-[-50%] focus:outline-none data-[state=open]:fade-in-90 data-[state=open]:slide-in-from-bottom-10">



                        {/* {toast.success("helloooooooooooo")} */}


                        <div className="bg-white  shadow-[hsl(206_22%_7%_/_35%)_0px_10px_38px_-10px,_hsl(206_22%_7%_/_20%)_0px_10px_20px_-15px]  transferModal    rounded-[1.125rem] py-10 px-6 sm:px-12   translate-x-[-50%]  ">
                            <div className="w-full max-md:overflow-auto flex justify-center py-6 items-center ">
                                <p className='text-lg font-semibold'>{message}</p>

                            </div>
                            <div className="max-md:fixed max-sm:bg-white sm:relative max-sm:flex justify-center  w-full  items-center border-none outline-none   max-md:h-24 max-sm:px-6 inset-x-0 bottom-0 ">
                                <Button className={`px-2 bg-primary flex items-center justify-center gap-x-3  h-14 w-full text-white rounded-lg font-semibold`} onClick={() => setOpen(false)}>Okay </Button>
                            </div>
                        </div>
                    </Dialog.Content>
                </Dialog.Portal>
            </Dialog.Root>

        </div>
    );
};

export default TranserConfirmModal;
