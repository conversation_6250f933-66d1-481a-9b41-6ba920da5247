'use client'
import React, { useEffect, useState } from 'react'
import SearchIcon from '../icons/SearchIcon';
interface Prop {
    debounce?: number;
    onChange: (payload: string) => void;
    value: string;
    className?: string;
    placeholder?: string
}
const DebounceInput = ({ value: initialValue, placeholder = 'Search for product', debounce = 500, onChange, className, ...props }: Prop) => {

    const [value, setValue] = useState(initialValue)
    useEffect(() => {
        setValue(initialValue)

    }, [initialValue])
    useEffect(() => {
        const timeout = setTimeout(() => {
            onChange(value)

        }, debounce)

        return () => clearTimeout(timeout)
    }, [value, debounce, onChange])


    return (
        <div className={`${className} justify-between bg-[#F8F8F8] h-[2.625rem] items-center px-[14px] gap-4 rounded-md `} >
            <input className='w-full bg-transparent px-2  outline-none h-full' placeholder={placeholder} type="text"
                value={value}
                onChange={(e) => setValue(e.target.value)}
                {...props}
            />

            <SearchIcon height={24} width={18} />
        </div >
    )
}

export default DebounceInput