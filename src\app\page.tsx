"use client"
import React, { useEffect, useRef, useState } from 'react'
import Image from 'next/image'
// import WebStoreHeader from './(web-store)/[webStoreId]/components/header-section/WebStoreHeader'
import { formatAxiosErrorMessage } from '@/utils/errors';
import { useErrorModalState } from '@/hooks';
import { Button, ErrorModal, LoaderModal } from '@/components/core';

import { useQuery } from '@tanstack/react-query';

import { AxiosError } from 'axios';

import { useScroll } from 'react-use';

import { Category, fetchWebStore, Headerimages } from './(web-store)/[webStoreId]/components/api/store/fetchStore';
import Header from './(web-store)/(instant-web)/components/shared/Header';
import Banner from './(web-store)/(instant-web)/components/shared/Banner';
import CategoryList from './(web-store)/(instant-web)/components/products/CategoryList';
import ProductList from './(web-store)/(instant-web)/components/products/ProductList';
import CheckoutSuccess from './(web-store)/(instant-web)/components/cart/CheckoutSuccess';
import Footer from './(web-store)/(instant-web)/components/shared/Footer';
import ColorBanner from './(web-store)/(instant-web)/components/icons/ColorBanner';
// import { Spinner } from '@/icons/core';

const Page = () => {

    const { isErrorModalOpen, setErrorModalState, closeErrorModal, openErrorModalWithMessage, errorModalMessage } = useErrorModalState();
    const ref = useRef<HTMLDivElement>(null);
    const { y } = useScroll(ref);

    const isScrolled = y > 50;
    const [domainUrl, setDomainUrl] = useState<string | null>(null);
    const [globalFilter, setGlobalFilter] = useState("");
    const [activeTab, setActiveTab] = useState("")
    const [showSuccessModal, setShowSuccessModal] = useState(false)
    const [showCartFullScreen, setShowCartFullScreen] = useState(false)

    const [orderId, setOrderId] = useState("")
    const [CheckoutDetails, setCheckoutDetails] = useState<{ first_nmae: string; last_name: string, phone_number: string; }>({
        first_nmae: "", last_name: "", phone_number: ""
    })
    // Get the scroll position using the useScroll hook...........

    useEffect(() => {
        setDomainUrl(window.location.hostname);
    }, []);



    // const extractedText = "alagomeji"

    function extractTextFromUrl(url: string) {
        const regex = /^(?:https?:\/\/)?(?:www\.)?([^\.]+)\.paybox360/;
        const match = url.match(regex);
        return match ? match[1] : '';
    }

    const extractedText = extractTextFromUrl(String("FirstGradeTokunboAlaba.paybox360.com"));
    console.log(extractedText, "text")
    // Use the subdomain in the query to fetch data
    const { data, isLoading } = useQuery(
        ["fetch-web-store", extractedText], // Query key
        () => fetchWebStore(extractedText as string), // Query function
        {
            enabled: !!extractedText && extractedText.trim() !== "" && !Number.isNaN(extractedText), // Check if extractedText is valid
            onError: (error: AxiosError) => {
                const errorMessage = formatAxiosErrorMessage(error);
                openErrorModalWithMessage(errorMessage);
            }
        }
    );






    return (
        <div className="bg-[#efefef] relative overflow-y-auto h-[100vh]" ref={ref}>
            {isLoading ? <LoaderModal /> :



                <>
                    {/* Sticky Header */}

                    <div className={` z-[1000]  ${isScrolled ? "sticky top-0 border-b-[0.3px]" : ""}`}>
                        <Header
                            branchId={data?.branch as string}
                            CheckoutDetails={CheckoutDetails}
                            companyId={data?.company_id as string}
                            globalFilter={globalFilter}
                            header_description={data?.header_description as string}
                            header_images={data?.header_images as Headerimages}

                            header_logo_text={data?.header_logo_text as string}
                            header_logos={data?.header_logos as Headerimages}
                            setCheckoutDetails={setCheckoutDetails}
                            setGlobalFilter={setGlobalFilter}
                            setOrderId={setOrderId}
                            setShowCartFullScreen={setShowCartFullScreen}
                            setShowSuccessModal={setShowSuccessModal}
                            showCartFullScreen={showCartFullScreen}
                            storeId={data?.store_id as string}
                        />
                    </div>;

                    <div className="relative  ">
                        {/* <div className={isSticky ? "py-5 bg-red-400 w-full" : "h-[8vh] bg-blue-400 w-full"}></div> */}
                        <div className="">
                            {
                                data?.header_banner_type === "image" ?
                                    <Banner imgsrc={data?.header_images?.url as string} /> : <div className="w-full   ">
                                        <ColorBanner bgColor={data?.banner_color} height={"100%"} width={"100%"} />
                                    </div>
                            }


                            <div className="py-3 md:py-7 z-[999]  w-full h-full absolute bottom-4 md:bottom-[-6rem]">
                                <div className="w-full px-2 sm:px-[2rem] lg:px-[5.75rem]">

                                    <div
                                        className={y > 130 ? "w-full py-5 md:py-3  fixed inset-x-0 top-[3rem] md:top-[6rem]  px-2 sm:px-[2rem] lg:px-[5.75rem] z-[99999999] bg-white" : ` `}

                                    >
                                        <CategoryList
                                            activeTab={activeTab}
                                            navigation_alignment={data?.logo_alignment as string}
                                            navigation_set_menu={data?.navigation_set_menu as Category[]}
                                            navigation_visible={data?.navigation_visible as boolean}
                                            setActiveTab={setActiveTab}
                                            setGlobalFilter={setGlobalFilter}
                                        />
                                    </div>

                                    <div className="mt-4 relative" style={{ zIndex: 1 }}>
                                        <ProductList
                                            activeTab={activeTab}
                                            CheckoutDetail={CheckoutDetails}
                                            globalFilter={globalFilter}
                                            id={data?.company_id as string}
                                            setOrderId={setOrderId}
                                            setShowSuccessModal={setShowSuccessModal}
                                            storeId={data?.store_id as string}
                                        />
                                        {data?.whatsapp_phone_number && <a href={`${data?.whatsapp_url}`} rel="noopener noreferrer" target='_blank'>
                                            <div className="fixed animate-pulse bottom-4 bg-[#60D669] w-[4rem] h-[4rem] rounded-full z-[9999999]  flex justify-center items-center right-[2rem] lg:right-[5rem] 2xl:right-[7rem]">
                                                <svg fill="none" height="44" viewBox="0 0 44 44" width="44" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M34.9245 9.00209C33.2434 7.30462 31.2414 5.9587 29.0349 5.04276C26.8285 4.12681 24.4618 3.65918 22.0728 3.66709C12.0628 3.66709 3.90448 11.8254 3.90448 21.8354C3.90448 25.0438 4.74781 28.1604 6.32448 30.9104L3.75781 40.3338L13.3828 37.8038C16.0411 39.2521 19.0295 40.0221 22.0728 40.0221C32.0828 40.0221 40.2411 31.8638 40.2411 21.8538C40.2411 16.9954 38.3528 12.4304 34.9245 9.00209ZM22.0728 36.9421C19.3595 36.9421 16.7011 36.2088 14.3728 34.8338L13.8228 34.5038L8.10281 36.0071L9.62448 30.4338L9.25781 29.8654C7.74998 27.4584 6.94951 24.6758 6.94781 21.8354C6.94781 13.5121 13.7311 6.72876 22.0545 6.72876C26.0878 6.72876 29.8828 8.30542 32.7245 11.1654C34.1318 12.5658 35.247 14.2318 36.0055 16.0665C36.7639 17.9013 37.1505 19.8684 37.1428 21.8538C37.1795 30.1771 30.3961 36.9421 22.0728 36.9421ZM30.3595 25.6488C29.9011 25.4288 27.6645 24.3288 27.2611 24.1638C26.8395 24.0171 26.5461 23.9438 26.2345 24.3838C25.9228 24.8421 25.0611 25.8688 24.8045 26.1621C24.5478 26.4738 24.2728 26.5104 23.8145 26.2721C23.3561 26.0521 21.8895 25.5571 20.1661 24.0171C18.8095 22.8071 17.9111 21.3221 17.6361 20.8638C17.3795 20.4054 17.5995 20.1671 17.8378 19.9288C18.0395 19.7271 18.2961 19.3971 18.5161 19.1404C18.7361 18.8838 18.8278 18.6821 18.9745 18.3888C19.1211 18.0771 19.0478 17.8204 18.9378 17.6004C18.8278 17.3804 17.9111 15.1438 17.5445 14.2271C17.1778 13.3471 16.7928 13.4571 16.5178 13.4388H15.6378C15.3261 13.4388 14.8495 13.5488 14.4278 14.0071C14.0245 14.4654 12.8511 15.5654 12.8511 17.8021C12.8511 20.0388 14.4828 22.2021 14.7028 22.4954C14.9228 22.8071 17.9111 27.3904 22.4578 29.3521C23.5395 29.8288 24.3828 30.1038 25.0428 30.3054C26.1245 30.6538 27.1145 30.5988 27.9028 30.4888C28.7828 30.3604 30.5978 29.3888 30.9645 28.3254C31.3495 27.2621 31.3495 26.3638 31.2211 26.1621C31.0928 25.9604 30.8178 25.8688 30.3595 25.6488Z" fill="white" />
                                                </svg>

                                            </div>
                                        </a>}
                                    </div>

                                    sdjfhkjdkdshfjdshfj
                                    <Image
                                        src='https://nyc3.digitaloceanspaces.com/requisition-space/media/display/file/WhatsApp_Image_2025-08-07_at_18.14.00_1_1JLY3Rt.jpeg?AWSAccessKeyId=DO00RFUZQBAXKQXVRK7W&Signature=%2BihZ8Pv1fmWO5WoHUmly6Z05%2FQc%3D&Expires=1754661785'
                                        width={300}
                                        height={400}
                                        alt="Test image"
                                    />
                                </div>
                                {/* Sticky Category List */}
                                <div className="">
                                    <Footer
                                        contact_address={data?.contact_address as string}
                                        contact_description={data?.contact_description as string}
                                        contact_email={data?.contact_email as string}
                                        contact_phone_number={data?.contact_phone_number as string}
                                        contact_visible={data?.contact_visible as boolean}
                                        facebook_link={data?.facebook_link as string}
                                        header_description={data?.header_description as string}
                                        header_images={data?.header_images as Headerimages}
                                        header_logo_text={data?.header_logo_text as string}
                                        header_logos={data?.header_logos as Headerimages}
                                        instagram_link={data?.instagram_link as string}
                                        whatsapp_phone_number={data?.whatsapp_phone_number as string}
                                        whatsapp_url={data?.whatsapp_url as string}
                                        x_link={data?.x_link as string}
                                    />
                                </div>
                            </div>

                        </div>
                    </div>

                </>
            }

            <ErrorModal
                isErrorModalOpen={isErrorModalOpen}
                setErrorModalState={setErrorModalState}
                subheading={errorModalMessage || 'Please check your inputs and try again.'}
            >
                <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                    <Button
                        className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
                        size="lg"
                        type="button"
                        onClick={closeErrorModal}
                    >
                        Okay
                    </Button>
                </div>
            </ErrorModal>

            {showSuccessModal && <CheckoutSuccess open={showSuccessModal} orderId={orderId} setOpen={setShowSuccessModal} setShowCartFullScreen={setShowCartFullScreen} />}

        </div>
    )
}

export default Page