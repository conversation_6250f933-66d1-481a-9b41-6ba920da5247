import * as React from "react";
import { SVGProps } from "react";
const SVGComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    fill="none"
    height={20}
    viewBox="0 0 20 20"
    width={20}
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M12.4998 18.9583H7.49984C2.97484 18.9583 1.0415 17.025 1.0415 12.5V7.49996C1.0415 2.97496 2.97484 1.04163 7.49984 1.04163H9.1665C9.50817 1.04163 9.7915 1.32496 9.7915 1.66663C9.7915 2.00829 9.50817 2.29163 9.1665 2.29163H7.49984C3.65817 2.29163 2.2915 3.65829 2.2915 7.49996V12.5C2.2915 16.3416 3.65817 17.7083 7.49984 17.7083H12.4998C16.3415 17.7083 17.7082 16.3416 17.7082 12.5V10.8333C17.7082 10.4916 17.9915 10.2083 18.3332 10.2083C18.6748 10.2083 18.9582 10.4916 18.9582 10.8333V12.5C18.9582 17.025 17.0248 18.9583 12.4998 18.9583Z"
      fill={props.fill || "#030229"}
    />
    <path
      d="M7.08311 14.7417C6.57478 14.7417 6.10811 14.5584 5.76645 14.225C5.35811 13.8167 5.18311 13.225 5.27478 12.6L5.63311 10.0917C5.69978 9.60838 6.01645 8.98338 6.35811 8.64172L12.9248 2.07505C14.5831 0.416716 16.2664 0.416716 17.9248 2.07505C18.8331 2.98338 19.2414 3.90838 19.1581 4.83338C19.0831 5.58338 18.6831 6.31672 17.9248 7.06672L11.3581 13.6334C11.0164 13.975 10.3914 14.2917 9.90811 14.3584L7.39978 14.7167C7.29145 14.7417 7.18311 14.7417 7.08311 14.7417ZM13.8081 2.95838L7.24145 9.52505C7.08311 9.68338 6.89978 10.05 6.86645 10.2667L6.50811 12.775C6.47478 13.0167 6.52478 13.2167 6.64978 13.3417C6.77478 13.4667 6.97478 13.5167 7.21645 13.4834L9.72478 13.125C9.94145 13.0917 10.3164 12.9084 10.4664 12.75L17.0331 6.18338C17.5748 5.64172 17.8581 5.15838 17.8998 4.70838C17.9498 4.16672 17.6664 3.59172 17.0331 2.95005C15.6998 1.61672 14.7831 1.99172 13.8081 2.95838Z"
      fill={props.fill || "#030229"}
    />
    <path
      d="M16.5418 8.19161C16.4835 8.19161 16.4251 8.18327 16.3751 8.16661C14.1835 7.54994 12.4418 5.80827 11.8251 3.61661C11.7335 3.28327 11.9251 2.94161 12.2585 2.84161C12.5918 2.74994 12.9335 2.94161 13.0251 3.27494C13.5251 5.04994 14.9335 6.45827 16.7085 6.95827C17.0418 7.04994 17.2335 7.39994 17.1418 7.73327C17.0668 8.01661 16.8168 8.19161 16.5418 8.19161Z"
      fill={props.fill || "#030229"}
    />
  </svg>
);
export default SVGComponent;
