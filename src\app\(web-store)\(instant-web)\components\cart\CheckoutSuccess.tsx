import React from 'react';
import * as Dialog from '@radix-ui/react-dialog';

// import { StockProps } from '@/app/(web-store)/[webStoreId]/components/types/products/producttype';
import CloseIcon from '../icons/CloseIcon';
import { <PERSON><PERSON>, <PERSON><PERSON>Header, ErrorModal } from '@/components/core';
import SuccessIcon from '../icons/SuccessIcon';
import { useQuery } from '@tanstack/react-query';
import { whatsappNotification } from '@/app/(web-store)/[webStoreId]/components/api/checkout/whatsappNotification';
import { useErrorModalState } from '@/hooks';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { AxiosError } from 'axios';
import { useRouter } from 'next/navigation';
import { SmallSpinner } from '@/icons/core';


interface Prop {
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    open: boolean;
    orderId: string
    setShowCartFullScreen: React.Dispatch<React.SetStateAction<boolean>>
}

const CheckoutSuccess = ({ open, setOpen, orderId }: Prop) => {
    const router = useRouter()
    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();
    const { refetch, isFetching } = useQuery({
        queryFn: () => whatsappNotification(orderId),
        queryKey: ["whatsaap-notification"],
        enabled: false,
        refetchInterval: 120000, // 120000 milliseconds = 2 minutes
        onSuccess: (data) => {
            router?.push(data?.message)
        },
        onError: (error) => {
            const errorMessage = formatAxiosErrorMessage(error as AxiosError);
            openErrorModalWithMessage(errorMessage);
        }
    }
    );
    return (
        <div className={`w-full z-[999999999999999999999999999999] `}>
            <Dialog.Root open={open}>
                <Dialog.Portal>
                    <Dialog.Overlay className="fixed inset-0  !z-[9999999999999999999999999999999]  bg-black/80 backdrop-blur-0 transition-all duration-100 data-[state=closed]:animate-out data-[state=closed]:fade-out data-[state=open]:fade-in" />
                    <Dialog.Content className={` w-[100%] overflow-x-hidden sm:max-w-[32.625rem] z-[999999999999] fixed left-[50%] max-md:bottom-0 md:top-[50%]  h-[25.6875rem] overflow-y-auto  translate-x-[-50%] md:translate-y-[-50%] rounded-[1.25rem] bg-white dark:bg-[#0D0D0D]  shadow-[hsl(206_22%_7%_/_35%)_0px_10px_38px_-10px,_hsl(206_22%_7%_/_20%)_0px_10px_20px_-15px] animate-in focus:outline-none`}>
                        <DialogHeader className="py-[1.25rem] px-4 md:px-[1.5rem] bg-white dark:bg-[#0D0D0D]  z-[9999] sticky top-0">
                            <Dialog.Title className='text-[#242424] text-xs md:text-base font-semibold dark:text-white'></Dialog.Title>
                            <Dialog.Close className="flex justify-center h-[2.625rem] w-[2.625rem] bg-[#EFEFEF] rounded-full items-center" onClick={() => setOpen(false)}>
                                <CloseIcon height={15} width={15} />
                            </Dialog.Close>
                        </DialogHeader>
                        <div className="flex flex-col justify-center   h-[20rem] items-center w-full overflow-x-hidden">
                            <div className="absolute top-5">
                                <SuccessIcon />
                            </div>
                            <div className=" absolute bottom-[1rem] overflow-x-hidden">

                                <div className="flex flex-col justify-center  items-center w-full overflow-x-hidden">
                                    <h2 className='text-[1.875rem] font-semibold font-outfit'>Order successful</h2>
                                    <p className='w-[80%] text-center text-base text-[#818181] py-1'>Your order has been made successfully.
                                        Thank you for choosing us!</p>
                                </div>
                                <div className="flex flex-row justify-between gap-4 mt-5 px-4">
                                    <Button className={`h-[3.0625rem] font-medium text-sm w-full bg-[#EFEFEF] text-black rounded-10 flex justify-center items-center gap-3`}
                                        onClick={() => refetch()}>Contact vendor {isFetching && <SmallSpinner color='blue' />}</Button>
                                    <Button className={`h-[3.0625rem] font-medium text-sm w-full bg-[#000] text-white rounded-10 flex justify-center items-center gap-3 `} onClick={() => {
                                        refetch()
                                        // setOpen(false)
                                        // setShowCartFullScreen(false)
                                    }}>Okay </Button>
                                </div>
                            </div>

                        </div>

                    </Dialog.Content>
                </Dialog.Portal>
            </Dialog.Root>

            <ErrorModal
                isErrorModalOpen={isErrorModalOpen}
                setErrorModalState={setErrorModalState}
                subheading={
                    errorModalMessage || 'Please check your inputs and try again.'
                }
            >
                <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                    <Button
                        className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
                        size="lg"
                        type="button"
                        onClick={closeErrorModal}
                    >
                        Okay
                    </Button>
                </div>
            </ErrorModal>

        </div>

    );
};

export default CheckoutSuccess;
