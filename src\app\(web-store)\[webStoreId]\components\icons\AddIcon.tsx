import * as React from "react";
import { SVGProps } from "react";

interface AddIconProps extends SVGProps<SVGSVGElement> {
    size?: number;
    color?: string;
}

const AddIcon: React.FC<AddIconProps> = ({
    size = 16,
    color = "#191C1F",
    ...props
}) => (
    <svg
        fill="none"
        height={size}
        viewBox="0 0 16 16"
        width={size}
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path
            d="M2.5 8H13.5"
            stroke={color}
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
        />
        <path
            d="M8 2.5V13.5"
            stroke={color}
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
        />
    </svg>
);

export default AddIcon;
