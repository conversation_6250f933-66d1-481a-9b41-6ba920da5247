"use client"
import React from 'react';
import * as Dialog from '@radix-ui/react-dialog';

import { Button } from '@/components/core';
import { checkSuccessProp } from './types';
import { convertKebabAndSnakeToTitleCase } from '@/utils/strings';
// import { DialogHeader } from '@/components/core';
import moment from "moment"
import { whatsappNotification } from '../api/checkout/whatsappNotification';
import { useRouter } from 'next/navigation';

interface Prop {
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    open: boolean;
    // message: string;
    checkoutResponse: checkSuccessProp | undefined
}
const PayOnDeliveryInvoiceModal = ({ open, setOpen, checkoutResponse }: Prop) => {
    const router = useRouter()
    return (
        <div className='bg-yellow-900 w-full'>
            <Dialog.Root open={open}>
                <Dialog.Portal>
                    <Dialog.Overlay className="fixed  inset-0 z-[9999999999999999] bg-[#efefef] backdrop-blur-2xl  transition-all duration-100 data-[state=closed]:animate-out data-[state=closed]:fade-out data-[state=open]:fade-in" />
                    <h2>in</h2>
                    <Dialog.Content className="w-[30.8125rem] z-[9999999999999999999999999999] max-w-[98%] lg:max-w-[71.4375rem] fixed left-[50%] max-md:bottom-0   md:top-[50%] animate-in md:translate-y-[-50%] focus:outline-none data-[state=open]:fade-in-90 data-[state=open]:slide-in-from-bottom-10">



                        {/* {toast.success("helloooooooooooo")} */}


                        <div className="bg-white  shadow-[hsl(206_22%_7%_/_35%)_0px_10px_38px_-10px,_hsl(206_22%_7%_/_20%)_0px_10px_20px_-15px] min-h-[24rem] max-h-[90vh]    rounded-[1.125rem] py-10 px-6 sm:px-12   translate-x-[-50%]  ">
                            <div className="w-full max-md:overflow-auto  py-6 ">
                                <div className="text-2xl font-semibold   "><h2>Invoice</h2></div>
                                <div className="flex items-start gap-x-3 mt-4">
                                    <p className='text-[#818181] text-sm w-[160px] shrink-0'>Company Name</p>
                                    <p className='font-medium text-sm'>{convertKebabAndSnakeToTitleCase(checkoutResponse?.checkout_order?.company)}</p>
                                </div>
                                <div className="flex items-start gap-x-3 mt-4">
                                    <p className='text-[#818181] text-sm w-[160px] shrink-0'>Order id</p>
                                    <p className='font-medium text-sm'>{convertKebabAndSnakeToTitleCase(checkoutResponse?.checkout_order?.order_id)}</p>
                                </div>
                                <div className="flex items-start gap-x-3 mt-4">
                                    <p className='text-[#818181] text-sm w-[160px] shrink-0'>Amount</p>
                                    <p className='font-medium text-sm'>	&#8358;{checkoutResponse?.checkout_order?.total_price}</p>
                                </div>
                                <div className="flex items-start gap-x-3 mt-4">
                                    <p className='text-[#818181] text-sm w-[160px] shrink-0'>Payment Status</p>
                                    <p className='font-medium text-sm'>	{convertKebabAndSnakeToTitleCase(checkoutResponse?.checkout_order?.payment_status)}</p>
                                </div>
                                <div className="flex items-start gap-x-3 mt-4">
                                    <p className='text-[#818181] text-sm w-[160px] shrink-0'>Customer Phone</p>
                                    <p className='font-medium text-sm'>	{`${checkoutResponse?.buyer_details?.phone_number}`}</p>
                                </div>
                                <div className="flex items-start gap-x-3 mt-4">
                                    <p className='text-[#818181] text-sm w-[160px] shrink-0'>Customer Name</p>
                                    <p className='font-medium text-sm'>	{`${checkoutResponse?.buyer_details?.first_name}  ${checkoutResponse?.buyer_details?.middle_name} ${checkoutResponse?.buyer_details?.last_name}`}</p>
                                </div>
                                <div className="flex items-start gap-x-3 mt-4">
                                    <p className='text-[#818181] text-sm w-[160px] shrink-0'>Customer Address</p>
                                    <p className='font-medium text-sm'>	{`${checkoutResponse?.buyer_details?.address}`}</p>
                                </div>
                                <div className="flex items-start gap-x-3 mt-4">
                                    <p className='text-[#818181] text-sm w-[160px] shrink-0'>Customer Email</p>
                                    <p className='font-medium text-sm'>	{checkoutResponse?.checkout_order?.buyer}</p>
                                </div>
                                <div className="flex items-start gap-x-3 mt-4">
                                    <p className='text-[#818181] text-sm w-[160px] shrink-0'>Payment Option</p>
                                    <p className='font-medium text-sm'>	Pay on Delivery</p>
                                </div>
                                <div className="flex items-start gap-x-3 mt-4">
                                    <p className='text-[#818181] text-sm w-[160px] shrink-0'>Time</p>
                                    <p className='font-medium text-sm'>	{moment(checkoutResponse?.checkout_order?.time, "HH:mm:ss.SSSSSS").format("hh:mm A")}</p>
                                </div>
                                <div className="flex items-start gap-x-3 mt-4">
                                    <p className='text-[#818181] text-sm w-[160px] shrink-0'>Date</p>
                                    <p className='font-medium text-sm'>	{moment(checkoutResponse?.checkout_order?.date)?.format("ll")}</p>
                                </div>

                            </div>
                            <div className="max-md:fixed max-sm:bg-white sm:relative max-sm:flex justify-center  w-full  items-start border-none4outline-none   max-md:h-24 max-sm:px-6 inset-x-0 bottom-0 ">
                                <Button className={`px-2 bg-primary flex items-center justify-cen4er gap-x-3  h-14 w-full text-white rounded-lg font-semibold`} onClick={() => {
                                    whatsappNotification(checkoutResponse?.checkout_order?.order_id as string).then((data) => {
                                        router?.push(data?.message)
                                        setOpen(false)
                                    })
                                }}>Okay </Button>
                            </div>
                        </div>
                    </Dialog.Content>
                </Dialog.Portal>
            </Dialog.Root>

        </div>
    );
};

export default PayOnDeliveryInvoiceModal;
