// import { payrollAxiosClient } from "@/lib/apiClient";
import axios from "axios";


export interface transferTypesProp {
    status: boolean;
    message: string;
    amount: number;
}


export const verifyTransfer = async (companyId: string, branchId: string, orderId: string) => {
    const response = await axios.get(`${process.env.NEXT_PUBLIC_API_STOCKS_BASE_URL}orders/verify_transfer?company=${companyId}&branch=${branchId}&order_id=${orderId}`);
    return response?.data as transferTypesProp;
};

// {{base_url}}/orders/