import React from 'react';
import * as Dialog from '@radix-ui/react-dialog';

// import { StockProps } from '@/app/(web-store)/[webStoreId]/components/types/products/producttype';
import CloseIcon from '../icons/CloseIcon';
import { DialogHeader } from '@/components/core';
import Image from 'next/image';


interface Prop {
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    open: boolean;
    imgSrc: string;

}

const ProductImagePreview = ({ open, setOpen, imgSrc }: Prop) => {

    return (
        <div className={`w-full z-[99999999999999999999999999] `}>
            <Dialog.Root open={open}>
                <Dialog.Portal>
                    <Dialog.Overlay className="fixed inset-0  z-[999999999999999]  bg-black/80 backdrop-blur-0 transition-all duration-100 data-[state=closed]:animate-out data-[state=closed]:fade-out data-[state=open]:fade-in" />
                    <Dialog.Content className={` w-[100%] overflow-x-hidden sm:max-w-[35.625rem]  z-[999999999999] fixed left-[50%] max-md:bottom-0 md:top-[50%]  h-[35.6875rem] overflow-y-auto  translate-x-[-50%] md:translate-y-[-50%] rounded-[1.25rem] bg-white dark:bg-[#0D0D0D]  shadow-[hsl(206_22%_7%_/_35%)_0px_10px_38px_-10px,_hsl(206_22%_7%_/_20%)_0px_10px_20px_-15px] animate-in focus:outline-none`}>
                        <DialogHeader className="py-[1.25rem] px-4 md:px-[1.5rem] bg-white dark:bg-[#0D0D0D]  z-[9999] sticky top-0">
                            <Dialog.Title className='text-[#242424] text-xs md:text-base font-semibold dark:text-white'></Dialog.Title>
                            <Dialog.Close className="flex justify-center h-[2.625rem] w-[2.625rem] bg-[#EFEFEF] rounded-full items-center" onClick={() => setOpen(false)}>
                                <CloseIcon height={15} width={15} />
                            </Dialog.Close>
                        </DialogHeader>
                        <div className="w-full  flex justify-center items-center">

                            <div className="h-[30rem] w-[30rem] sm:w-[32rem] relative bg-[#f4f3f1] flex justify-center items-center" onClick={() => {
                                setOpen(true);
                            }}>

                                <Image
                                    alt={'Product image'}
                                    className='object-cover rounded-[1.25rem]'
                                    layout='fill'
                                    src={imgSrc}
                                />

                            </div>
                        </div>

                    </Dialog.Content>
                </Dialog.Portal>
            </Dialog.Root>



        </div>

    );
};


export default ProductImagePreview
