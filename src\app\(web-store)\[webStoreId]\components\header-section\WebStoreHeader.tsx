"use client";
import Image from 'next/image';
import React, { useEffect, useState } from 'react';
import SearchIcon from '../icons/SearchIcon';
import CartIcon from '../icons/CartIcon';
import CartList from '../cart/CartList';
import BillingDetails from '../checkout/BillingDetails';

// import {
//     Tabs,
//     TabsContent,
//     TabsList,
//     TabsTrigger,
// } from '@/components/core';
import { cn } from '@/utils/classNames';
import ListStoreProduct from '../products/ListStoreProduct';
import MobileCategoryModal from './MobileCategoryModal';
import { useInfiniteQuery, useQuery } from '@tanstack/react-query';
import { fetchCategory } from '../api/products/fetchCategory';
import { generateColorArray } from '../utils/generateColor';
// import { fetchProdcuts } from '../api/products/fetchProducts';
import DebounceInput from './DebounceInput';
import { ProductTpes } from '../types/products/producttype';
import axios from 'axios';
import useCartStore from '../store/cartStore';
// import { Category } from '@/app/(dashboard)/instant-web/customize-website/types/categoryListTypes';
// import { imgProp } from '@/app/(dashboard)/instant-web/misc/store';
import InstantWebFooter from './InstantWebFooter';
import { Headerimages } from '../api/store/fetchStore';
// import { Button } from '@/components/core';
// import { Button } from '@/components/core';
import { useSearchParams } from 'next/navigation';

export interface Category {
    id: string;
    created_at: string;
    name: string;
    company: null | string;
    created_by: string;
    company_name: string;
    has_products: boolean;
    stock_count: number;
}

interface queryKeyTypes {
    pageParam: number;
    filterUrl?: string;
    id: string,
    seletedTab: string,
    globalFilter: string,
    page?: number,
    size: number
}
export type contactInfo = {

    name: string;
    email: string;
    phone_number: string;


}




// customise

interface Prop {
    storeId: string;
    header_description: string;
    header_images: Headerimages;
    header_logos: Headerimages;
    header_logo_text: string;
    type: string;
    id: string;
    navigation_alignment: string;
    navigation_set_menu: Category[];
    navigation_visible: boolean
    contact_visible: boolean;
    interface_theme: "LIGHT" | "DARK" | "SYSTEM"
    store_brand_color: string;
    contact_phone_number: string
    contact_email: string
    contact_address: string
    contact_description: string;
    target_id: string;
    logo_alignment: string
}
const WebStoreHeader = ({ header_description,
    header_images,
    header_logos,
    header_logo_text, type,
    navigation_alignment,
    navigation_set_menu, navigation_visible,
    interface_theme, store_brand_color,
    id,
    contact_address,
    contact_description,
    contact_visible,
    contact_email, contact_phone_number,
    target_id,
    logo_alignment,
    storeId


}: Prop) => {
    // const cart = useCartStore((state) => state?.cart)
    // const totalQuantity = cart?.reduce((sum, item) => sum + item.quantity, 0);
    const cart = useCartStore((state) => state.carts[storeId]);
    const searchParams = useSearchParams();
    // const [hasCategoryInParams, setHasCategoryInParams] = useState(false); // Track if categories are in params

    const [filteredCategories, setFilteredCategories] = useState<Category[]>(navigation_set_menu);
    const totalQuantity = cart?.reduce((sum, item) => sum + item.quantity, 0) || 0;
    const [darkMode] = useState(interface_theme === "DARK" ? true : false)
    const [globalFilter, setGlobalFilter] = useState("");
    const [openCart, setOpenCart] = useState(false);
    const [openBillingModal, setOpenBillingModal] = useState(false);
    const [openSearch, setOpenSearch] = useState(false);
    const [showMobileCategoryModal, setShowMobileCategoryModal] = useState(false);
    const [contactInfoProps, setContactInfoProps] = useState<contactInfo>({
        email: "",
        name: "",
        phone_number: ""

    })

    // const id = "0340ea7d-c5be-455a-8377-b0c28c523508";
    const { data, isLoading: loadingCategory } = useQuery({
        queryFn: () => fetchCategory(id),
        queryKey: ['fetch-products-category', id],
        enabled: !!id
    });
    const color = generateColorArray();


    useEffect(() => {
        // Get the 'category' query parameters from the URL
        const categoryIds = searchParams.getAll('category');

        // Check if there are categories in the URL params
        if (categoryIds.length > 0) {
            // setHasCategoryInParams(true); // Set to true if categories exist in params
            // Filter the categories based on the URL params
            const filtered = navigation_set_menu.filter((category) =>
                categoryIds.includes(category.id)
            );
            setFilteredCategories(filtered);
        } else {
            // setHasCategoryInParams(false); // No categories in params, show all categories
            setFilteredCategories(navigation_set_menu);
        }
    }, [navigation_set_menu, searchParams]);

    const [seletedTab, setSeletedTab] = useState(filteredCategories[0]?.id ?? "");
    const size = 10


    const fetchProducts = async ({ id, globalFilter, seletedTab, pageParam = 1, size }: queryKeyTypes) => {

        const { data } = await axios.get(
            `${process.env.NEXT_PUBLIC_API_STOCKS_BASE_URL}instant_web/company_available_stock?company_id=${id}&category_id=${seletedTab}&search=${globalFilter}&page=${pageParam}&size=${size} `
        );
        return data as ProductTpes;
    };


    const {
        data: productData,
        error,
        fetchNextPage,
        hasNextPage,
        isFetchingNextPage,
        isLoading,
    } = useInfiniteQuery(
        ['fetch-products', id, globalFilter, seletedTab],

        ({ pageParam = 1 }) => fetchProducts({ id, seletedTab, globalFilter, size, pageParam }),

        {
            staleTime: 6000,
            enabled: !!id,
            getNextPageParam: (lastPage, pages) => {
                if (lastPage?.next !== null) {
                    return pages.length + 1;
                } else {
                    return;
                }
            }
        }
    );



    return (
        <div className={` ${interface_theme === "DARK" ? "dark" : ""}`}>

            <div className=' w-full  dark:bg-[#0D0D0D] @container' >
                <div className={`fixed top-0 inset-x-0 hide_img_on_desktop  mobile-overlay flex w-full justify-between gap-x-5 items-center px-8  py-6 ${type === "customise" ? "lg:px-[2.75rem]" : "lg:px-[8.75rem]"}`}
                    style={{
                        backgroundImage: `url("/images/web-store/storeBanner.png")`,
                        backgroundRepeat: 'no-repeat',
                        backgroundPosition: 'center',
                        backgroundSize: 'cover',
                        position: 'relative',
                        backgroundBlendMode: "color"
                    }}
                >
                    <div className=" flex-1">
                        <div className="flex gap-[12.3696px] w-full   items-center cursor-pointer" style={{ justifyContent: logo_alignment?.toLowerCase() }}>
                            {header_logos?.url && <div className="hidden justify-center md:flex items-center h-[50px] w-[50px]  overflow-hidden">
                                <Image alt='store-logo' height={48} src={header_logos?.url} width={48} />
                            </div>}
                            <div>
                                <h2 className='font-semibold text-sm text-white md:text-black md:text-[22px] dark:md:text-white' style={{ wordBreak: "break-word" }}>{header_logo_text}</h2>
                                <p className='font-medium w-[95%] md:w-auto text-white md:text-black text-[10px] md:text-xs dark:md:text-white' style={{ wordBreak: "break-word" }}>{header_description}</p>
                            </div>

                        </div>
                    </div>
                    <div className="flex items-center gap-x-[22px]">
                        <DebounceInput
                            className='hidden md:flex'
                            value={globalFilter ?? ""}
                            onChange={(value) => setGlobalFilter(String(value))}
                        />
                        <div className="px-2 bg-white cursor-pointer w-8 h-8 rounded-full flex justify-center items-center md:hidden" onClick={() => setOpenSearch(!openSearch)}>
                            <SearchIcon />
                        </div>
                        <button className="cursor-pointer bg-white md:bg-transparent w-8 h-8 rounded-full flex justify-center items-center relative" onClick={() => setOpenCart(true)}>
                            <CartIcon className='w-4 md:w-8' />
                            {totalQuantity > 0 && <div className="bg-red-500 text-white rounded-full px-2 py-1 shrink-0 text-xs absolute -top-2 -right-0">{totalQuantity}</div>}
                        </button>
                    </div>
                </div>

                <div className={`sticky  top-[80px] py-[.9375rem] md:py-[26px] mb-2  ${darkMode && "dark"}`} style={{ borderTop: `1px solid ${store_brand_color}` }}>
                    {openSearch && <div className='flex px-5 w-full pb-3'>
                        <DebounceInput
                            className='flex md:hidden'
                            value={globalFilter ?? ""}
                            onChange={(value) => setGlobalFilter(String(value))}
                        />
                    </div>}
                    <div className={`${navigation_visible ? "hidden  md:flex dark:text-white" : "hidden"}  relative   shadow-md  gap-2  items-left w-full lg:w-auto ${type === "customise" ? "xl:px-[3.75rem]" : "xl:px-[8.75rem]"}`} style={{ justifyContent: navigation_alignment?.toLowerCase() }}>
                        {/* {!loadingCategory && !hasCategoryInParams && (
                            <button
                                className={cn(
                                    `relative px-6 font-semibold text-xs py-[.625rem] pb-4 shrink-0 dark:text-white`,
                                    // { 'text-[#D68F1C]': seletedTab === "" },
                                    // { 'text-[#394157]': seletedTab !== "" }
                                )}
                                style={{
                                    color: seletedTab === "" ? store_brand_color : `${interface_theme === "DARK" ? "#fff" : "#394157"}`
                                }}
                                onClick={() => setSeletedTab("")}
                            >
                                All
                                {seletedTab === "" && <div className="absolute bottom-0 inset-x-0 h-1 " style={{ backgroundColor: store_brand_color }}></div>}
                            </button>
                        )} */}
                        <div className={`flex gap-2 overflow-x-auto custom-scrollbar relative`}>
                            {filteredCategories?.map((tab) => (
                                <button
                                    className={cn(
                                        `relative font-semibold text-xs py-[.625rem] pb-4 mx-2`
                                    )}
                                    key={tab?.id}
                                    style={{
                                        color: seletedTab === tab?.id ? store_brand_color : interface_theme === "DARK" ? "#fff" : "#394157",
                                        whiteSpace: "nowrap", // Prevent text from wrapping
                                        // wordBreak: "revert-layer"
                                    }}
                                    onClick={() => setSeletedTab(tab?.id)}
                                >
                                    {tab?.name}
                                    {seletedTab === tab?.id && (
                                        <div
                                            className="absolute -bottom-0 inset-x-0 h-1"
                                            style={{ backgroundColor: store_brand_color }}
                                        ></div>
                                    )}
                                </button>
                            ))}
                        </div>

                        {/* {!loadingCategory && navigation_set_menu?.length > 3 && (
                            <Button
                                className='px-6 rounded-[.625rem] z-50 py-0 block text-black h-10  font-semibold text-xs mt-[-.3125rem]  shrink-0 dark:text-white hover:scale-95'
                                style={{ backgroundColor: color[3] }}
                                onClick={() => setShowMobileCategoryModal(true)}
                            >
                                All category
                            </Button>
                        )} */}
                    </div>

                    <div className={`w-full z-50 pb-4 shadow-md dark:text-white flex gap-2 justify-center items-center ${navigation_visible ? "flex md:hidden " : "hidden"}`}>
                        {/* {!loadingCategory && !hasCategoryInParams && (
                            <button
                                className={cn(
                                    'relative px-6 font-semibold text-xs py-[.625rem] dark:text-white',
                                    // { 'text-[#D68F1C]': seletedTab === "" },
                                    // { 'text-[#394157]': seletedTab !== "" }

                                )}
                                style={{
                                    color: seletedTab === "" ? store_brand_color : `${interface_theme === "DARK" ? "#fff" : "#394157"}`
                                }}
                                onClick={() => setSeletedTab("")}
                            >
                                All
                                {seletedTab === "" && <div className="absolute bottom-0 inset-x-0 h-1 " style={{ backgroundColor: store_brand_color }}></div>}
                            </button>
                        )} */}
                        {filteredCategories?.slice(0, 1)?.map((cart) => (
                            <button
                                className={cn(
                                    'relative font-semibold text-xs py-[7px] mx-2 dark:text-white',
                                    // { 'text-[#D68F1C]': seletedTab === cart?.id },
                                    // { 'text-[#394157]': seletedTab !== cart?.id }
                                )}
                                key={cart?.id}
                                style={{
                                    color: seletedTab === cart?.id ? store_brand_color : `${darkMode ? "#fff" : "#394157"} `
                                }}
                                onClick={() => setSeletedTab(cart?.id)}
                            >
                                {cart?.name}
                                {seletedTab === cart?.id && <div className="absolute bottom-0 inset-x-0 h-1 " style={{ backgroundColor: store_brand_color }}></div>}
                            </button>
                        ))}
                        {!loadingCategory && navigation_set_menu?.length > 1 && (
                            <button
                                className='px-6 rounded-[.625rem] font-semibold text-xs py-2'
                                style={{ backgroundColor: color[3] }}
                                onClick={() => setShowMobileCategoryModal(true)}
                            >
                                All Categories
                            </button>
                        )}
                    </div>


                    <main className="flex items-center flex-wrap overflow-y-auto" id={target_id}>
                        <div className="w-full overflow-y-auto h-[75vh]">
                            <ListStoreProduct darkMode={interface_theme === "DARK" ? "dark" : ""} data={productData} error={error} fetchNextPage={fetchNextPage} hasNextPage={hasNextPage} header_images={header_images?.url} id={id} interface_theme={interface_theme} isFetchingNextPage={isFetchingNextPage} isLoading={isLoading} storeId={storeId} type={type} />
                        </div>
                    </main>
                    <footer className={`${contact_visible ? "block" : "hidden"}`}>
                        <InstantWebFooter
                            contact_address={contact_address}
                            contact_description={contact_description}
                            contact_email={contact_email}
                            contact_phone_number={contact_phone_number}
                            darkMode={interface_theme === "DARK" ? "dark" : ""}
                            header_description={header_description}
                            header_images={header_images}
                            header_logo_text={header_logo_text}
                            header_logos={header_logos}
                        />
                    </footer>
                </div>
                {showMobileCategoryModal && <MobileCategoryModal categories={data?.data?.categories} colors={color} navigation_set_menu={navigation_set_menu} open={showMobileCategoryModal} seletedTab={seletedTab} setOpen={setShowMobileCategoryModal} setSeletedTab={setSeletedTab} />}
                {openCart && <CartList darkMode={interface_theme === "DARK" ? "dark" : ""} open={openCart} setContactInfoProps={setContactInfoProps} setOpen={setOpenCart} setOpenBilling={setOpenBillingModal} storeId={storeId} />}
                {openBillingModal && <BillingDetails contactInfoProps={contactInfoProps} open={openBillingModal} setOpen={setOpenBillingModal} storeId={storeId} />}
            </div>

        </div>
    );
}

export default WebStoreHeader;
