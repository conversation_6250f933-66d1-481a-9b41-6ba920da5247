export interface CheckoutTypes {
    id: string;
    amount_paid: string;
    status: string;
    payment_status: string;
    buyer: Buyer;
    order_date: string;
    order_time: string;
    current_stage: Currentstage;
    order_products: Orderproduct[];
    trail: null;
    shipping: number;
    discount: number;
    tax: number;
    contact_name: string;
    contact_phone_number: string;
    additional_information: string;
    ship_to_different_address: boolean;
    total_price: number;
    whatsapp_url: string;
    company_id: string;
    company_name: string;
    branch_id: string;
    branch_name: string;
}


interface Orderproduct {
    product_name: string;
    product_description: string;
    product_img: string;
    quantity: number;
    price: string;
    sub_total: string;
    payment_option: string;
}

interface Currentstage {
    id: string;
    created_at: string;
    updated_at: string;
    name: string;
    email_subject: null;
    email_body: null;
    email_text: null;
    email_notification_enabled: boolean;
    order_count: null;
    position: number;
    pipeline: string;
}

interface Buyer {
    id: string;
    created_at: string;
    updated_at: string;
    first_name: string;
    middle_name: string;
    last_name: string;
    country: string;
    city: string;
    state: string;
    email: string;
    phone_number: string;
    address: string;
    postal_code: null;
    status: string;
    ship_to_different_address: boolean;
}