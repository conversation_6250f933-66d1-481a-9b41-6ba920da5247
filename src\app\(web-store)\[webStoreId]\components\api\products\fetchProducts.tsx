import axios from "axios";
import { ProductTpes } from "../../types/products/producttype";

export const fetchProdcuts = async (id: string, seletedCategory: string, search: string, page: number, size: number) => {
    const { data } = await axios.get(
        `${process.env.NEXT_PUBLIC_API_STOCKS_BASE_URL}instant_web/company_available_stock?company_id=${id}&category_id=${seletedCategory}&search=${search}&page=${page}&size=${size} `
    );
    return data as ProductTpes;
};

