"use client"
import React, { useState } from 'react';
import * as Dialog from '@radix-ui/react-dialog';
import { DialogHeader } from '@/components/core';
import CartTable from './CartTable';
import CartSideBar from './CartSideBar';
import useCartStore from '../store/cartStore';
import { contactInfo } from '../header-section/WebStoreHeader';
// import { useQuery } from '@tanstack/react-query';
// import { fetchCart } from '../api/cart/fetchCart';


interface Prop {
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    open: boolean;
    setOpenBilling: React.Dispatch<React.SetStateAction<boolean>>
    setContactInfoProps: React.Dispatch<React.SetStateAction<contactInfo>>
    darkMode: string | boolean;
    storeId: string
}

const CartList = ({ open, setOpen, setOpenBilling, setContactInfoProps, darkMode, storeId }: Prop) => {
    const cart = useCartStore((state) => state.carts[storeId]);
    const [data, setData] = useState(cart);
    const totalQuantity = data?.reduce((sum, item) => sum + item.quantity, 0);
    // const { data: cardData } = useQuery({
    //     queryFn: fetchCart,
    //     queryKey: ["fetch-cart-itemsx"]
    // })
    // console.log(cardData, "cardData");

    return (
        <div className={`bg-yellow-900 w-full ${darkMode ? "dark" : ""}`}>
            < Dialog.Root open={open} >
                <Dialog.Portal>
                    <Dialog.Overlay className="fixed  z-[99999999999999999999999999] inset-0  bg-black/80 backdrop-blur-md transition-all duration-100 data-[state=closed]:animate-out data-[state=closed]:fade-out data-[state=open]:fade-in" />
                    <Dialog.Content className={`max-w-[98%] z-[999999999999]  lg:max-w-[71.4375rem] overflow-y-auto fixed left-[50%] max-md:bottom-0  md:top-[50%] max-h-[84vh]  md:h-[90vh] w-max translate-x-[-50%] md:translate-y-[-50%] rounded-lg bg-white dark:bg-[#0D0D0D] shadow-[hsl(206_22%_7%_/_35%)_0px_10px_38px_-10px,_hsl(206_22%_7%_/_20%)_0px_10px_20px_-15px] animate-in focus:outline-none data-[state=open]:fade-in-90 data-[state=open]:slide-in-from-bottom-10 ${darkMode ? "dark" : ""}`}>

                        <DialogHeader className="py-[1.25rem] lg:py-8 px-3 md:px-[3.2rem] bg-white dark:bg-[#0D0D0D] z-[9999] sticky top-0">
                            <Dialog.Title className='text-[#242424] font-sans text-lg md:text-[2rem] font-semibold dark:text-white'>Shopping cart</Dialog.Title>
                            <Dialog.Close className=" flex justify-center items-center"> <svg fill="none" height="25" viewBox="0 0 32 32" width="25" xmlns="http://www.w3.org/2000/svg" onClick={() => setOpen(false)}>
                                <path d="M14.1142 16.0001L3.72363 5.60949L5.60925 3.72388L15.9998 14.1143L26.3903 3.72388L28.2759 5.60949L17.8854 16.0001L28.2759 26.3905L26.3903 28.2762L15.9998 17.8857L5.60925 28.2762L3.72363 26.3905L14.1142 16.0001Z" fill="#596072" />
                            </svg></Dialog.Close>
                        </DialogHeader>
                        <div className="relative max-md:max-h-[75vh] max-lg:h-[85vh]  max-md:pb-[8rem] px-3 md:px-[3.5rem] md:grid grid-cols-1 pb-[3.5rem] overflow-y-auto lg:grid-cols-[1.8fr_1fr] gap-5">
                            <div className="relative overflow-y-auto border-[.0625rem]  border-[#E4E7E9] rounded ">
                                <h2 className='py-[1.25rem] px-6 text-base md:text-lg text-[#191C1F] dark:text-white'>Shopping Cart ({totalQuantity ?? 0})</h2>
                                <CartTable data={data} setData={setData} storeId={storeId} />
                            </div>
                            <div className="">
                                <CartSideBar data={data} setCartModal={setOpen} setContactInfoProps={setContactInfoProps} setOpenBilling={setOpenBilling} />
                            </div>

                        </div>

                    </Dialog.Content>
                </Dialog.Portal>
            </ Dialog.Root>
        </div >
    );
};

export default CartList;
