name: Deploy to DigitalOcean

on:
  push:
    branches:
      - webstore

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      # - name: Install dependencies
      #   run: npm install

      # - name: Build Next.js app
      #   run: npm run build

      - name: Add SSH key
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.SERVER_SSH_KEY }}

      - name: Deploy to DigitalOcean
        env:
          HOST: **************
          USER: root
        run: |
          ssh -o StrictHostKeyChecking=no $USER@$HOST <<EOF
            # Navigate to your app directory
            cd /home/<USER>

            # Pull latest changes
            git pull origin webstore

            # Install dependencies and restart the app
            npm install

            pm2 kill
          pm2 start npm -- run production
          EOF
