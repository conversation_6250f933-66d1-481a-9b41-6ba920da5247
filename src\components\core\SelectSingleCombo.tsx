import * as React from "react"
import { FieldErrors, FieldValues } from "react-hook-form"

import { cn } from "@/utils/classNames"
import { convertKebabAndSnakeToTitleCase } from "@/utils/strings"

import {
    Command,
    CommandGroup,
    CommandItem,
} from "./Command"
// import { <PERSON>t<PERSON>own, <PERSON>Spin<PERSON>, StrokeCheck } from "../icons"
import { Popover, PopoverContent, PopoverTrigger } from "./Popover"
import { <PERSON>Spin<PERSON>, StrokeCheck } from "@/icons/core"
import { Input } from "./Input"
import SearchIcon from "@/app/(web-store)/[webStoreId]/components/icons/SearchIcon"

interface Option {
    id?: string;
    label?: string;
    name?: string;
    value?: string;
}

interface SelectProps {
    value: string | boolean | undefined;
    onChange: (value: string) => void;
    options: Option[] | undefined;
    placeholder: string;
    errors?: { [key: string]: { message?: string | undefined } } | FieldErrors<FieldValues>;
    label?: string | React.ReactNode;
    name: string;
    className?: string;
    containerClass?: string;
    labelClass?: string;
    itemClass?: string;
    errorClass?: string;
    fullWidth?: boolean
    withIcon?: boolean
    isLoadingOptions?: boolean
    triggerColor?: string
    valueKey?: "id" | "value" | "name";
    readableTextKey?: keyof Option;
}

const ComboboxDemo: React.FC<SelectProps> = ({
    value,
    onChange,
    options,
    errors,
    label,
    name,
    className,
    containerClass,
    labelClass,
    itemClass,
    errorClass,
    fullWidth,
    withIcon,
    isLoadingOptions,
    valueKey,
    readableTextKey,
    triggerColor }) => {
    const [open, setOpen] = React.useState(false)
    const [optionsToDisplay, setOptionsToDisplay] = React.useState<Option[] | undefined>(options)
    const [searchText, setSearchText] = React.useState<string>("")
    // const handleSelect = (currentValue: string | boolean) => {
    //     onChange(String(currentValue))
    //     setOpen(false)
    // }
    React.useEffect(() => {
        if (searchText) {
            const filteredOptions = options?.filter(option => {
                return (option.label && option.label.toLowerCase().includes(searchText.toLowerCase())) ||
                    (option.name && option.name.toLowerCase().includes(searchText.toLowerCase())) ||
                    (option.value && option.value.toLowerCase().includes(searchText.toLowerCase())) ||
                    (option.id && option.id.toLowerCase().includes(searchText.toLowerCase())) ||
                    (option[valueKey!] && option[valueKey!]?.toLowerCase().includes(searchText.toLowerCase()));
            });
            setOptionsToDisplay(filteredOptions);
        } else {
            setOptionsToDisplay(options);
        }
    }, [searchText, options, valueKey])

    const getOptionLabel = (option: Option) => {
        if (value && options) {
            if (readableTextKey) {
                return option[readableTextKey]
            }
            else if (valueKey) {
                return option[valueKey]
            }
            else {
                return option.name || option.value || option.id
            }
        } else {
            return `Select ${convertKebabAndSnakeToTitleCase(name).toLowerCase()}`
        }
    }

    const handleSelect = (currentValue: string | boolean) => {
        const selectedOption = options?.find(option => {
            const optionValue = option[valueKey!]?.toLowerCase() || option.value?.toLowerCase();
            return optionValue === String(currentValue).toLowerCase();
        }) || {};
        const selectedValue = selectedOption.value || selectedOption[valueKey!] || '';
        onChange(String(selectedValue));
        getOptionLabel(selectedOption);
        setOpen(false);
    }



    const triggerRef = React.useRef<HTMLDivElement | null>(null)
    const [width, setWidth] = React.useState<string>("50%")
    React.useEffect(() => {
        if (triggerRef?.current) {
            setWidth(`${triggerRef.current.clientWidth}px`)
        }
    }, [triggerRef?.current?.clientWidth])




    return (
        <div className={cn("inputdiv", withIcon && "withicon", containerClass)}>
            {label && <label className={cn(labelClass)} htmlFor={name}>{label}</label>}

            <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                    <div
                        aria-expanded={open}
                        className={cn(
                            "flex h-max w-full items-center justify-between gap-2 rounded-lg px-3.5 py-2 sm:px-4 !text-[13px] bg-[#F2F5FF] text-sm !font-semibold capitalize !text-[#032282]",
                            "ring-offset-white transition duration-300 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50",
                            "data-[placeholder]:text-[#032282] border-2 border-transparent focus:border-primary !overflow-hidden whitespace-nowrap cursor-pointer",
                            className, fullWidth ? "max-w-full" : "max-w-[520px]"
                        )}
                        ref={triggerRef}
                        role="combobox"
                        onClick={() => setOpen(!open)}
                    >
                        <span className={cn(
                            '!overflow-hidden text-[0.785rem] ',
                            value && options && options?.length ? '!text-[#032382] font-medium' : '!text-[#032382a1]'
                        )}>
                            {(value && options && options?.length)
                                ? getOptionLabel(options.find(option => option[valueKey!]?.toLowerCase() === value.toString()?.toLowerCase()) || {})
                                : `Select ${convertKebabAndSnakeToTitleCase(name).toLowerCase()}`
                            }
                        </span>
                        <svg
                            className={cn("ml-2  shrink-0 opacity-70 transition-transform duration-300", open && "rotate-180")}
                            fill="none"
                            height={7}
                            viewBox="0 0 12 7"
                            width={12}
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                className={cn('fill-label-text')}
                                clipRule="evenodd"
                                d="M8.357 5.522a3.333 3.333 0 0 1-4.581.126l-.133-.126L.41 2.089A.833.833 0 0 1 1.51.84l.078.07L4.82 4.342c.617.617 1.597.65 2.251.098l.106-.098L10.411.91a.833.833 0 0 1 1.248 1.1l-.07.079-3.232 3.433Z"
                                fill={triggerColor || "#032282"}
                                fillRule="evenodd"
                            />
                        </svg>

                    </div>
                </PopoverTrigger>

                <PopoverContent className={cn("p-0", triggerRef?.current && `min-w-max`)} style={{ width }} >
                    <Command>
                        {/* <CommandInput placeholder={`Search ${convertKebabAndSnakeToTitleCase(name).toLowerCase()}`} /> */}
                        <div className="relative px-6">
                            <SearchIcon className="absolute top-1/2 left-2 -translate-y-1/2 text-[#032282] h-4 w-4" />
                            <Input
                                className="focus:!ring-0 !ring-0 bg-white pl-4" placeholder={`Search ${convertKebabAndSnakeToTitleCase(name).toLowerCase()}`}
                                type="text"
                                onChange={(e) => setSearchText(e.target.value)}
                            />
                        </div>
                        <CommandGroup>
                            {isLoadingOptions &&
                                <CommandItem value={"loading"} disabled>
                                    <SmallSpinner color='#000000' /> Loading options...
                                </CommandItem>
                            }
                            {!isLoadingOptions && options && options?.length > 0 ?
                                optionsToDisplay?.map((option, index) => (
                                    <div
                                        className={cn("grid grid-cols-[max-content_1fr] text-xs",
                                            "relative flex cursor-default select-none items-center rounded-md px-2 py-1.5 outline-none aria-selected:bg-blue-100/70 aria-selected:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
                                            itemClass)}
                                        key={index}
                                        onClick={() => handleSelect(option[valueKey!] || option.name || option.value || option.id || "")}
                                    >
                                        <StrokeCheck
                                            className={cn(
                                                "mr-2 h-4 w-4",
                                                valueKey && option[valueKey] === value
                                                    ? "opacity-100"
                                                    : option.value && value === option.value
                                                        ? "opacity-100"
                                                        : option.id && value === option.id
                                                            ? "opacity-100"
                                                            : option.id && value === option.id?.toLowerCase()
                                                                ? "opacity-100"
                                                                : "opacity-0"
                                            )}
                                        />
                                        {option.label || option.name}
                                    </div>
                                ))
                                : <CommandItem className={cn("text-[0.8125rem]", itemClass)} value={""} disabled>
                                    There&apos;s no option to select from
                                </CommandItem>
                            }
                        </CommandGroup>
                    </Command>
                </PopoverContent>
            </Popover>

            {errors && errors[name] && <span className={cn('formerror', errorClass)}>{String(errors[name]?.message)}</span>}
        </div>
    )
}

export default ComboboxDemo
