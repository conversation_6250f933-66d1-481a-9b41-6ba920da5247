import { useQuery } from "@tanstack/react-query";
import axios from "axios";

interface whatsappTypes {
    error: boolean;
    message: string;
}
export const whatsappNotification = async (orderId: string) => {
    const response = await axios.get(`${process.env.NEXT_PUBLIC_API_STOCKS_BASE_URL}orders/vendor-whatsapp-notification?order_id=${orderId}`);
    return response?.data as whatsappTypes;
};


export const useWhatsappNotification = (orderId: string) => {
    return useQuery({
        queryFn: () => whatsappNotification(orderId),
        queryKey: ["whatsaap-notification", orderId],
        enabled: !!orderId
    })
}