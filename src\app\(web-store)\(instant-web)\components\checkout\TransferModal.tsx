import React, { useState } from 'react';
import * as Dialog from '@radix-ui/react-dialog';


import CloseIcon from '../icons/CloseIcon';
import {
    DialogHeader, ErrorModal, Button,

} from '@/components/core';
import BasketIcon from '../icons/BasketIcon';
import ShippingBus from '../icons/ShippingBus';
import CardIcon from '../icons/CardIcon';

import { z } from 'zod';

import { useClipboard, useErrorModalState } from '@/hooks';
import CopyIcon from '../icons/CopyIcon';
import { useQuery } from '@tanstack/react-query';
import { verifyTransfer } from '@/app/(web-store)/[webStoreId]/components/api/checkout/verifyTransfer';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { AxiosError } from 'axios';
import { SmallSpinner } from '@/icons/core';
import ConfirmTransferModal from './ConfirmTransferModal';
// import CheckoutSuccess from './CheckoutSuccess';

interface Prop {
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    open: boolean;
    // darkMode: string | boolean;
    storeId: string;
    transferData: {
        amount: string;
        bank_name: string;
        bank_code: number;
        account_name: string;
        account_number: string;
        contact_phone_number: string;
        company_name: string;
        branchId: string;
        orderId: string;
        companyId: string;
    } | undefined
    setOrderId: React.Dispatch<React.SetStateAction<string>>
    closeCheckoutModal: React.Dispatch<React.SetStateAction<boolean>>
}

const contactSchema = z.object({
    buyer: z.object({
        first_name: z.string().min(1, { message: "Enter first name" }),
        last_name: z.string().min(1, { message: "Enter last name" }),
        middle_name: z.string(),
        address: z.string().min(1, { message: "Enter address" }),
        country: z.string().min(1, { message: "Enter country" }),
        state: z.string().min(1, { message: "Enter state" }),
        city: z.string().min(1, { message: "Enter city" }),
        email: z.string().email().min(1, { message: "Enter email" }),
        addition_information: z.string().optional(),
        phone_number: z.string({ required_error: "Enter phone_number." }).min(5, { message: "Enter phone_number" }),
        ship_to_different_address: z.boolean(),
        paymentOption: z.enum(['CARD', 'TRANSFER', 'DELIVERY', 'USSD', 'CASH']),
    })
});


export type contactInfoProps = z.infer<typeof contactSchema>;

const TransferCheckoutModal = ({ open, setOpen, transferData, setOrderId, closeCheckoutModal }: Prop) => {
    const { copy } = useClipboard()
    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();


    const [showConfirmModal, setShowConfirmModal] = useState(false)
    // const [transferMsg, setTransferMsg] = useState("")
    const { refetch, isFetching } = useQuery({
        queryFn: () => verifyTransfer(transferData?.companyId as string, String(transferData?.branchId), String(transferData?.orderId)),
        queryKey: ["verify-transfer"],
        enabled: false,
        refetchInterval: 10000, // 120000 milliseconds = 2 minutes
        onSuccess: (data) => {
            if (data?.status) {
                setOrderId(transferData?.orderId as string)
                setOpen(false)
                closeCheckoutModal(false)
                // transferMsg
            } else {
                setShowConfirmModal(true)
                // setTransferMsg(data?.message)
            }
        },
        onError: (error) => {
            const errorMessage = formatAxiosErrorMessage(error as AxiosError);
            openErrorModalWithMessage(errorMessage);
        }
    }
    );

    // useEffect(() => {
    //     // Start the interval when the component mounts
    //     refetch();
    // }, [refetch]);

    return (
        <div className={`w-full z-[9999] `}>
            <Dialog.Root open={open}>
                <Dialog.Portal>
                    <Dialog.Overlay className="fixed inset-0  z-[9999999]  bg-black/80 backdrop-blur-0 transition-all duration-100 data-[state=closed]:animate-out data-[state=closed]:fade-out data-[state=open]:fade-in" />
                    <Dialog.Content className={`overflow-y-auto fixed right-0 bottom-0   md:top-0   h-[100vh] w-full md:w-[36.375rem] z-[9999999] rounded-lg bg-white dark:bg-[#0D0D0D] shadow-[hsl(206_22%_7%_/_35%)_0px_10px_38px_-10px,_hsl(206_22%_7%_/_20%)_0px_10px_20px_-15px] animate-in focus:outline-none data-[state=open]:fade-in-90 data-[state=open]:slide-in-from-bottom-10`}>
                        <DialogHeader className="py-[1rem] px-4 md:px-[1.5rem] bg-white dark:bg-[#0D0D0D] border-black z-[9999] sticky top-0">
                            <div className="flex justify-between w-full items-center">

                                <div className='flex items-center' onClick={() => {
                                    setOpen(false)
                                    // setShowCart(true)
                                }}>
                                    <div className="flex justify-center cursor-pointer  rounded-full items-center" onClick={() => {
                                        setOpen(false)
                                        // setShowCart(true)
                                    }}>
                                        <svg fill="none" height="20" viewBox="0 0 28 20" width="28" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M12.4984 17.2253C12.3401 17.2253 12.1818 17.1669 12.0568 17.0419L6.62344 11.6086C5.7401 10.7253 5.7401 9.27526 6.62344 8.39193L12.0568 2.95859C12.2984 2.71693 12.6984 2.71693 12.9401 2.95859C13.1818 3.20026 13.1818 3.60026 12.9401 3.84193L7.50677 9.27526C7.10677 9.67526 7.10677 10.3253 7.50677 10.7253L12.9401 16.1586C13.1818 16.4003 13.1818 16.8003 12.9401 17.0419C12.8151 17.1586 12.6568 17.2253 12.4984 17.2253Z" fill="#292D32" />
                                            <path d="M20.4984 17.2253C20.3401 17.2253 20.1818 17.1669 20.0568 17.0419L14.6234 11.6086C13.7401 10.7253 13.7401 9.27526 14.6234 8.39193L20.0568 2.95859C20.2984 2.71693 20.6984 2.71693 20.9401 2.95859C21.1818 3.20026 21.1818 3.60026 20.9401 3.84193L15.5068 9.27526C15.1068 9.67526 15.1068 10.3253 15.5068 10.7253L20.9401 16.1586C21.1818 16.4003 21.1818 16.8003 20.9401 17.0419C20.8151 17.1586 20.6568 17.2253 20.4984 17.2253Z" fill="#292D32" />
                                        </svg>


                                        <p className='text-black'>Back to checkout</p>
                                    </div>

                                </div>

                                <div className="">


                                    <div className="flex justify-center h-8 w-8 bg-[#EFEFEF] rounded-full items-center" onClick={() => setOpen(false)}>
                                        <Button className='bg-transparent'>  <CloseIcon height={10} width={10} /></Button>
                                    </div>
                                </div>
                            </div>

                        </DialogHeader>
                        <div className=" flex items-center gap-7  p-6">
                            <div className="flex items-center gap-2">
                                <div className="w-[2rem] h-[2rem] rounded-full bg-[#F8F8F8] flex justify-center items-center">
                                    <Button className='bg-transparent'>  <BasketIcon color='black' height={15} width={15} /></Button>
                                </div>
                                <p className='text-[#818181]'>Cart</p>
                            </div>
                            <div className="flex items-center gap-2">
                                <div className="w-[2rem] h-[2rem] rounded-full bg-[#F8F8F8] flex justify-center items-center">
                                    <Button className='bg-transparent' >  <ShippingBus fill='black' height={15} width={15} /></Button>
                                </div>
                                <p className='text-[#818181]'>Shipping details</p>
                            </div>
                            <div className="flex items-center gap-2">
                                <div className="w-[2rem] h-[2rem] rounded-full bg-black flex justify-center items-center">
                                    <Button className='bg-transparent' >  <CardIcon color={"#fff"} /></Button>
                                </div>
                                <p className='text-black'>Payment</p>
                            </div>

                        </div>
                        <div className="px-6 -mt-3 pb-3">
                            <p className='text-xs font-semibold text-[#818181] '>Payment: Complete purchases with various payment options.

                            </p>
                        </div>
                        <div className="bg-[#F4F4F4]  w-full px-6 py-3">
                            <div className="bg-white rounded-xl space-y-1 p-[1.25rem]">
                                <div className="flex items-center gap-2">
                                    <div className="w-6 h-6 rounded-full bg-[#EFEFEF] flex justify-center items-center">
                                        <Button className='bg-transparent' >  <CardIcon size={15} /></Button>
                                    </div>
                                    <p className='text-black text-sm'>Amount to pay</p>
                                </div>
                                <p className='text-black text-[1.375rem] font-bold font-sans'>&#8358;{Number(transferData?.amount).toLocaleString()}</p>

                                <p className=' text-xs text-[#818181]'>Transfer amount shown above to complete this purchase</p>
                            </div>
                        </div>
                        <div className="bg-[#F4F4F4]  mt-3 w-full px-6 py-8">
                            <div className="bg-white rounded-xl flex text-center justify-center items-center flex-col p-[1.25rem] ">
                                <p className='text-[#696969] text-sm'>Bank account details</p>
                                <p className='text-[#696969] text-xs'>Account number</p>
                                <div className="flex items-center ">
                                    <p className='text-[1.875rem] font-bold'>{transferData?.account_number}</p>
                                    <Button className='bg-transparent' onClick={() => copy(transferData?.account_number as string)}><CopyIcon /></Button>
                                </div>
                                <div className="w-full bg-[#F7F7F7] grid grid-cols-2 p-4  mt-1 rounded-10">
                                    <div className="text-center">
                                        <p className='text-xs text-[#A2A2A2]'>Account name</p>
                                        <h2 className='text-base font-medium font-sans'>{transferData?.account_name}</h2>
                                    </div>
                                    <div className="text-center">
                                        <p className='text-xs text-[#A2A2A2]'>Bank name</p>
                                        <h2 className='text-base font-medium font-sans'>{transferData?.bank_name}</h2>
                                    </div>
                                </div>
                                <div className="w-full bg-[#e0f4ef] gap-3 mt-[.5625rem] rounded-md flex items-center justify-center p-[.6875rem]"><svg fill="none" height="20" viewBox="0 0 20 20" width="20" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M9.98532 1.68011C5.40934 1.68747 1.67138 5.43747 1.67874 10.0134C1.6861 14.5894 5.4361 18.3274 10.0121 18.32C14.5881 18.3127 18.326 14.5627 18.3187 9.98669C18.3113 5.41071 14.5613 1.67275 9.98532 1.68011Z" stroke="#00A37D" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" />
                                    <path d="M10.0039 13.3281L9.99722 9.16815" stroke="#00A37D" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" />
                                    <path d="M10 6.67188L9.99253 6.67189" stroke="#00A37D" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" />
                                </svg>
                                    <p className='text-xs text-[#00A37D]'>Upon successful transfer, tab button below to confirm transfer</p></div>
                            </div>
                        </div>
                        <div className="  mt-4 w-full px-6 py-3">
                            <div className="bg-white rounded-xl flex text-center justify-center items-center flex-col p-[1.25rem]">
                                <Button className='bg-black text-white rounded-10 h-[3.0625rem] w-full flex justify-center items-center gap-3' onClick={() => refetch()}>I have made the transfer {isFetching && <SmallSpinner color='#fff' />}</Button>
                            </div>
                            <p className='mt-3 text-xs text-center'>Upon payment confirmation, vendor will be notified via  <br />WhatsApp and other channels for easy processing</p>
                        </div>


                    </Dialog.Content>
                </Dialog.Portal>
            </Dialog.Root>

            <ErrorModal
                isErrorModalOpen={isErrorModalOpen}
                setErrorModalState={setErrorModalState}
                subheading={
                    errorModalMessage || 'Please check your inputs and try again.'
                }
            >
                <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                    <Button
                        className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
                        size="lg"
                        type="button"
                        onClick={closeErrorModal}
                    >
                        Okay
                    </Button>
                </div>
            </ErrorModal>

            {showConfirmModal && <ConfirmTransferModal open={showConfirmModal} refetch={refetch} setOpen={setShowConfirmModal} />}
        </div>

    );
};

export default TransferCheckoutModal;
