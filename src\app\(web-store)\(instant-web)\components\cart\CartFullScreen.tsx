import React, { useEffect, useState } from 'react';
import * as Dialog from '@radix-ui/react-dialog';
import { <PERSON><PERSON>, DialogHeader, ErrorModal } from '@/components/core';


import CloseIcon from '../icons/CloseIcon';
import Image from 'next/image';

import useCartStore, { CartItem } from '@/app/(web-store)/[webStoreId]/components/store/cartStore';
import FullScreenIcon from '../icons/FullScreenIcon';
import BasketIcon from '../icons/BasketIcon';
import ShippingBus from '../icons/ShippingBus';
import CardIcon from '../icons/CardIcon';
import MinusIcon from '@/app/(web-store)/[webStoreId]/components/icons/MinusIcon';
import AddIcon from '@/app/(web-store)/[webStoreId]/components/icons/AddIcon';
import RemoveIcon from '../icons/RemoveIcon';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import CheckoutDetails from '../checkout/CheckoutDetails';
import { CheckoutName, useAnonymusUser } from '@/app/(web-store)/[webStoreId]/components/api/checkout/anonymusUser';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { AxiosError } from 'axios';
import { useErrorModalState } from '@/hooks';
import { SmallSpinner } from '@/icons/core';

interface Prop {
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    open: boolean;
    setShowCart: React.Dispatch<React.SetStateAction<boolean>>
    setShowSuccessModal: React.Dispatch<React.SetStateAction<boolean>>
    storeId: string
    setData: React.Dispatch<React.SetStateAction<CartItem[]>>
    setOrderId: React.Dispatch<React.SetStateAction<string>>
    setCheckoutDetails: React.Dispatch<React.SetStateAction<{
        first_nmae: string;
        last_name: string;
        phone_number: string;
    }>>
    CheckoutDetail: {
        first_nmae: string;
        last_name: string;
        phone_number: string;
    }
    branchId: string;
    companyId: string
}
const contactSchema = z.object({
    contact: z.object({
        name: z.string().min(1, { message: "Enter full name" })
            .refine(value => {
                const names = value.trim().split(/\s+/);
                return names.length === 2; // Ensure exactly two names
            }, {
                message: "Please enter exactly two names",
            }),
        phone_number: z.string({ required_error: "Enter phone_number." }).min(11, { message: "Enter valid phone_number" }).max(11, { message: "Enter valid phone_number" }),
    })
});
export type contactInfoProps = z.infer<typeof contactSchema>;

const CartFullScreen = ({ open, setOpen, storeId, setShowCart, setShowSuccessModal, setOrderId, setCheckoutDetails, CheckoutDetail, companyId }: Prop) => {
    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();
    const carts = useCartStore((state) => state.carts[storeId]);
    const removecart = useCartStore((state) => state.removeCart);
    const [data, setData] = useState(carts);
    const totalQuantity = data?.reduce((sum, item) => sum + item.quantity, 0);
    const deleteFromCart = useCartStore((state) => state?.deleteFromCart)
    const updateCart = useCartStore((state) => state?.updateCart)
    const cart = useCartStore((state) => state.carts[storeId]);
    const [showCheckoutModal, setShowCheckoutModal] = useState(false)

    const handleQuantityChange = (cartId: string, rowIndex: number, increment: number) => {
        const currentCartItem = cart?.find(item => item?.id === cartId);
        if (!currentCartItem) return;

        const newQuantity = Math.max(0, currentCartItem?.quantity + increment); // Ensure quantity doesn't drop below 0

        if (newQuantity <= 0) {

            deleteFromCart(storeId, cartId); // Remove item from cart if quantity reaches 0
            setData(data?.filter((prod => String(prod?.id) !== String(cartId))))
        } else {
            updateCart(storeId, cartId, newQuantity);
        }


        setData((prevData) =>
            prevData?.map((item, index) =>
                index === rowIndex
                    ? {
                        ...item,
                        quantity: Math.max(0, item?.quantity + increment), // Prevents quantity from going below 1
                        subTotal: item.price * Math.max(1, item?.quantity + increment),
                    }
                    : item
            )
        );

    };
    const handleRemoveCart = (id: string) => {
        setData(data?.filter((prod => String(prod?.id) !== String(id))))
        deleteFromCart(storeId, id); // Remove item from cart 
    }

    const totalSum = data.reduce((sum, item) => sum + item.subTotal, 0);

    // Example values for shipping, discount, and tax
    const shipping = 0; // Free shipping
    const discount = 0; // Example discount value
    const tax = 0; // Example tax value

    // Calculate the grand total
    const grandTotal = totalSum + shipping - discount + tax;





    const {
        register,
        handleSubmit,
        formState: { errors },
    } = useForm<contactInfoProps>({
        resolver: zodResolver(contactSchema),
        defaultValues: {
            contact: {
                name: "",
                phone_number: ""
            }

        },
        mode: "onChange",


    });
    const { mutate: handleSaveUser, isLoading } = useAnonymusUser()
    const onSubmit = (data: contactInfoProps) => {
        handleSaveUser({
            products: cart,
            name: data?.contact?.name,
            phone_number: data?.contact?.phone_number,
            // branch: branchId,
            company: companyId,
            web_store: storeId
        }, {
            onSuccess: (data: CheckoutName) => {
                const name = data?.name?.split(' ');

                setCheckoutDetails({

                    first_nmae: name[0],
                    last_name: name[1],
                    phone_number: data?.phone as string
                })
                setShowCheckoutModal(true)
                // setOpen(false)
            },
            onError: (error) => {
                const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                openErrorModalWithMessage(errorMessage);
            }
        })

    }
    useEffect(() => {
        if (cart?.length === 0) {
            setOpen(false)
        }
    }, [cart?.length, setOpen])

    return (
        <div className={`w-full z-[9999] `}>
            <Dialog.Root open={open}>
                <Dialog.Portal>
                    <Dialog.Overlay className="fixed inset-0  z-[9999999]  bg-black/80 backdrop-blur-0 transition-all duration-100 data-[state=closed]:animate-out data-[state=closed]:fade-out data-[state=open]:fade-in" />
                    <Dialog.Content className={`overflow-y-auto fixed right-0 bottom-0   md:top-0   h-[100vh] w-full md:w-[36.375rem] z-[9999999] rounded-lg bg-white dark:bg-[#0D0D0D] shadow-[hsl(206_22%_7%_/_35%)_0px_10px_38px_-10px,_hsl(206_22%_7%_/_20%)_0px_10px_20px_-15px] animate-in focus:outline-none data-[state=open]:fade-in-90 data-[state=open]:slide-in-from-bottom-10`}>
                        <DialogHeader className="py-[1.25rem] px-4 md:px-[1.5rem] bg-white dark:bg-[#0D0D0D] border-opacity-10 border-b-[.0187rem] border-black z-[9999] sticky top-0">
                            <div className="flex justify-between w-full items-center">
                                <div className="flex items-center gap-3">
                                    <p className='font-medium text-lg text-black'>Cart </p>
                                    <div className=" w-[2rem] h-[2rem] rounded-full bg-[#EFEFEF] flex  justify-center items-center">
                                        <p className='text-black text-sm font-semibold'>{totalQuantity}</p>
                                    </div>
                                </div>
                                <div className="flex items-center gap-3">
                                    <div onClick={() => removecart(storeId)}>
                                        <Button className=' h-[2.375rem] rounded-[.625rem]' disabled={data?.length === 0} variant={"outlined"}>Clear</Button>
                                    </div>

                                    <div className="flex justify-center h-8 w-8 bg-[#EFEFEF] rounded-full items-center" onClick={() => {
                                        setOpen(false)
                                        setShowCart(true)
                                    }}>
                                        <Button className='bg-transparent'> <FullScreenIcon /></Button>
                                    </div>
                                    <div className="flex justify-center h-8 w-8 bg-[#EFEFEF] rounded-full items-center" onClick={() => setOpen(false)}>
                                        <Button className='bg-transparent'>  <CloseIcon height={10} width={10} /></Button>
                                    </div>
                                </div>

                            </div>

                        </DialogHeader>

                        <div className="">

                        </div>
                        <div className=" flex items-center gap-3 md:gap-7 p-6">
                            <div className="flex items-center gap-2">
                                <div className="w-[2rem] h-[2rem] rounded-full bg-black flex justify-center items-center">
                                    <Button className='bg-transparent'>  <BasketIcon color='white' height={15} width={15} /></Button>
                                </div>
                                <div className="">
                                    <p className='text-black'>Cart</p>


                                </div>
                            </div>
                            <div className="flex items-center gap-2">
                                <div className="w-[2rem] h-[2rem] rounded-full bg-[#F8F8F8] flex justify-center items-center">
                                    <Button className='bg-transparent' >  <ShippingBus color='black' height={15} width={15} /></Button>
                                </div>
                                <p className='text-[#818181]'>Shipping details</p>
                            </div>
                            <div className="flex items-center gap-2">
                                <div className="w-[2rem] h-[2rem] rounded-full bg-[#F8F8F8] flex justify-center items-center">
                                    <Button className='bg-transparent' >  <CardIcon /></Button>
                                </div>
                                <p className='text-[#818181]'>Payment</p>
                            </div>

                        </div>
                        <div className="px-6 -mt-3 pb-3">
                            <p className='text-xs font-semibold text-[#818181] '> Cart: Review and adjust items before checkout.

                            </p>
                        </div>


                        <div className=" h-[40vh] md:h-[53vh] overflow-y-auto">

                            {
                                data?.map((cartItem, idx: number) => (
                                    <div className="flex items-center justify-between px-6 border-b-[0.3px] border-opacity-20 py-4" key={idx}>
                                        <div className="">
                                            <div className="flex items-start gap-3">
                                                <div className='relative bg-[#f4f3f1] w-[5rem] h-[4.375rem] p-2 rounded-10 flex justify-center items-center'>
                                                    {cartItem?.product_img !== null ? (
                                                        <Image
                                                            alt={``}
                                                            className="rounded-lg"
                                                            height={65}
                                                            src={cartItem?.product_img}
                                                            width={75}
                                                        />
                                                    ) : (
                                                        <div className="w-[3.25rem] h-[2.75rem] rounded-lg"></div>
                                                    )}
                                                </div>
                                                <div className="flex flex-col gap-2">
                                                    <p className='text-sm font-medium font-outfit'>{cartItem?.product_name}</p>

                                                    <div className='flex   rounded items-center  gap-x-[.2813rem]'>
                                                        <Button className='p-1 bg-[#F9F9F9] w-[2.625rem] h-[1.8rem] md:h-[2.1875rem] rounded-lg flex justify-center items-center' onClick={() => handleQuantityChange(String(cartItem?.id), idx, -1)}>
                                                            <MinusIcon className='w-[10px] md:w-4' fill='white' />
                                                        </Button>
                                                        <input
                                                            className='max-w-[2.6875rem] border-[#E8E6E6] font-bold border-[.0187rem]  bg-transparent outline-none h-[2.4375rem] rounded-[.3125rem] text-center text-base text-black dark:text-white'
                                                            type='number'
                                                            value={data?.find(cart => cart.id === cartItem?.id)?.quantity}
                                                            readOnly
                                                        />
                                                        <Button className='p-1 bg-[#F9F9F9] w-[2.625rem] h-[1.8rem] md:h-[2.1875rem] rounded-lg flex justify-center items-center' onClick={() => handleQuantityChange(String(String(cartItem?.id)), idx, 1)}>
                                                            <AddIcon className='w-[10px] md:w-4' fill='white' />
                                                        </Button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="flex flex-col items-center gap-3">
                                            <p className='text-black text-xl font-bold font-sans'>&#8358;{Number(cartItem?.subTotal).toLocaleString()}</p>
                                            <div className="flex items-center gap-2 cursor-pointer" onClick={() => handleRemoveCart(cartItem?.id)}>
                                                <div className="w-[2rem] h-[2rem] rounded-full bg-[#f2e5e5] flex justify-center items-center">
                                                    <Button className='bg-transparent' >  <RemoveIcon color='black' height={15} width={15} /></Button>
                                                </div>
                                                <p className='text-[#818181]'>Remove</p>
                                            </div>
                                        </div>
                                    </div>
                                ))
                            }
                        </div>
                        <div className="flex items-center justify-between p-6">
                            <p className='text-[#080808] text-lg'>Items total:</p>
                            <h2 className='text-black text-xl font-bold font-outfit'>&#8358;{Number(grandTotal).toLocaleString()}</h2>
                        </div>
                        <div className=" h-[30vh] md:h-[20vh] px-6">
                            <form onSubmit={handleSubmit(onSubmit)}>
                                <div className=" rounded-10   p-4 w-full mt-3 border-[.0187rem]">
                                    <div className="flex gap-3 items-center">
                                        <p className='text-[#080808] text-base'>Personal details </p>
                                        <p className='text-[#818181] text-xs'>(for ease of communication)</p>
                                    </div>
                                    <div className="flex flex-col md:flex-row justify-between gap-4 mt-2">

                                        <div className="w-full">
                                            <input placeholder='Enter name' type="text" {...register("contact.name")} className={`border-[0.5px] rounded-10 px-4 h-[2.75rem]  w-full text-sm outline-none border-opacity-30 ${errors?.contact?.name ? "border border-red-400" : ""}`} />
                                            {errors?.contact?.name && <span className='text-xs font-clash text-red-900 '>{errors?.contact?.name?.message}</span>}
                                        </div>
                                        <div className="w-full">
                                            <input maxLength={11} placeholder='Phone number' type="number" {...register("contact.phone_number")} className={`border-[0.5px] rounded-10 px-4 h-[2.75rem] w-full  text-sm outline-none border-opacity-30 ${errors?.contact?.phone_number ? "border border-red-400" : ""}`}


                                                onKeyDown={(e) => {
                                                    // Prevent non-numeric keys (e.g., e, +, -, .)
                                                    if (!/[0-9]/.test(e.key) && e.key !== "Backspace" && e.key !== "Delete" && e.key !== "ArrowLeft" && e.key !== "ArrowRight") {
                                                        e.preventDefault();
                                                    }
                                                }}

                                            />
                                            {errors?.contact?.phone_number && <span className='text-xs font-clash text-red-900 '>{errors?.contact?.phone_number?.message}</span>}

                                        </div>
                                    </div>
                                </div>
                                <div className="flex  justify-between gap-4 mt-2">
                                    <Button className={`h-[3.0625rem] font-medium text-xs  sm:text-sm w-full bg-[#EFEFEF] text-black rounded-10 `} type='button' onClick={() => setOpen(false)}>Add more items</Button>
                                    <Button className={`h-[3.0625rem] font-medium text-xs sm:text-sm w-full bg-[#000] text-white rounded-10 flex justify-center items-center`}> {isLoading ? <SmallSpinner color='#fff' /> : "Proceed to checkout"}</Button>
                                </div>

                            </form>
                        </div>
                    </Dialog.Content>
                </Dialog.Portal>
            </Dialog.Root>
            <ErrorModal
                isErrorModalOpen={isErrorModalOpen}
                setErrorModalState={setErrorModalState}
                subheading={
                    errorModalMessage || 'Please check your inputs and try again.'
                }
            >
                <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                    <Button
                        className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
                        size="lg"
                        type="button"
                        onClick={closeErrorModal}
                    >
                        Okay
                    </Button>
                </div>
            </ErrorModal>


            {
                showCheckoutModal && <CheckoutDetails
                    CheckoutDetails={CheckoutDetail}
                    open={showCheckoutModal}
                    setOpen={setShowCheckoutModal}
                    setOrderId={setOrderId}
                    setShowCart={setOpen}
                    setShowSuccessModal={setShowSuccessModal}
                    storeId={storeId}

                />
            }
        </div >

    );
};

export default CartFullScreen;
