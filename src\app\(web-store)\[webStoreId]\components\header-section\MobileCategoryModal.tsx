import React, { useState } from 'react';
import * as Dialog from '@radix-ui/react-dialog';
import { DialogHeader } from '@/components/core';
import { CategoryList } from '../types/products/categoryType';

// import {
//     TabsList,
//     TabsTrigger,
// } from '@/components/core';
import { cn } from '@/utils/classNames';
import DebounceInput from './DebounceInput';
import { Category } from '../api/store/fetchStore';

interface Prop {
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    setSeletedTab: React.Dispatch<React.SetStateAction<string>>
    navigation_set_menu: Category[]
    seletedTab: string
    open: boolean;
    categories: CategoryList[] | undefined
    colors: string[];

}

const MobileCategoryModal = ({ open, setOpen, colors, seletedTab, setSeletedTab, navigation_set_menu }: Prop) => {
    const [globalFilter, setGlobalFilter] = useState("");
    const filterCategoriesByName = (categories: Category[], searchName: string) => {
        return categories?.filter(category =>
            category?.name?.toLowerCase()?.includes(searchName?.toLowerCase())
        );
    };
    const [filteredCategories, setFilteredCategories] = useState(navigation_set_menu);

    const handleSearch = (searchValue: string) => {
        setGlobalFilter(searchValue);
        const filtered = filterCategoriesByName(navigation_set_menu, searchValue);
        setFilteredCategories(filtered);
    };

    return (
        <div className='w-full '>
            <Dialog.Root open={open}>
                <Dialog.Portal>
                    <Dialog.Overlay className="fixed z-50 inset-0 bg-black/80 backdrop-blur-lg  transition-all duration-100 data-[state=closed]:animate-out data-[state=closed]:fade-out data-[state=open]:fade-in" />
                    <Dialog.Content className="max-w-[98%] lg:max-w-[50.4375rem] fixed z-[9999] left-[50%] bottom-0 categoryModal max-h-[95vh] min-h-[5vh] overflow-y-auto w-full translate-x-[-50%] translate-y-0 rounded-t-lg bg-white shadow-[hsl(206_22%_7%_/_35%)_0px_10px_38px_-10px,_hsl(206_22%_7%_/_20%)_0px_10px_20px_-15px] animate-in focus:outline-none data-[state=open]:fade-in-90 data-[state=open]:slide-in-from-bottom-10">
                        <DialogHeader className="py-[1.25rem] px-4 flex flex-col md:px-[3.5rem] bg-white z-[9999] sticky top-0">
                            <div className="flex w-full justify-between items-center">
                                <Dialog.Title className='text-[#242424] text-lg md:text-[2rem] font-semibold'>All categories</Dialog.Title>
                                <Dialog.Close className=" flex justify-center items-center bg-[#F3F4F6] w-[1.875rem] h-[1.875rem] rounded-full"> <svg fill="none" height="18" viewBox="0 0 32 32" width="18" xmlns="http://www.w3.org/2000/svg" onClick={() => setOpen(false)}>
                                    <path d="M14.1142 16.0001L3.72363 5.60949L5.60925 3.72388L15.9998 14.1143L26.3903 3.72388L28.2759 5.60949L17.8854 16.0001L28.2759 26.3905L26.3903 28.2762L15.9998 17.8857L5.60925 28.2762L3.72363 26.3905L14.1142 16.0001Z" fill="#596072" />
                                </svg></Dialog.Close>

                            </div>

                            <div className="w-full"> <DebounceInput
                                className='flex w-full'
                                value={globalFilter ?? ""}
                                onChange={handleSearch}
                            /></div>
                        </DialogHeader>


                        <div className=" w-full flex gap-3 px-4 flex-wrap items-center mb-10">
                            {
                                filteredCategories?.map((cart, idx: number) => (
                                    <React.Fragment key={cart?.id}>

                                        <button
                                            className={cn(
                                                'font-semibold text-xs py-[.625rem] mx-2 p-2 rounded-md',
                                                // { 'text-[#D68F1C] border-b-2 border-[#D68F1C]': seletedTab === cart?.id },
                                                // { 'text-[#394157]': seletedTab !== cart?.id }
                                            )}
                                            key={cart?.id}
                                            style={{ background: seletedTab === cart?.id ? "blue" : colors[idx], color: seletedTab === cart?.id ? "white" : "#000" }}
                                            onClick={() => {
                                                setSeletedTab(cart?.id)
                                                setOpen(false)
                                            }}
                                        >
                                            {cart?.name}
                                        </button>
                                    </React.Fragment>
                                ))

                            }
                        </div>


                    </Dialog.Content>
                </Dialog.Portal>
            </Dialog.Root>
        </div>

    );
};

export default MobileCategoryModal;
