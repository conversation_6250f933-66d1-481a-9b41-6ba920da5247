'use client';

import * as React from 'react';

import {
  Button,
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
  LinkButton,
} from '@/components/core';

export default function Error({ error }: { error: Error; reset: () => void }) {
  React.useEffect(() => {
    // Log the error to an error reporting service
    console.error(error.message);
  }, [error]);

  return (
    <div className="flex h-full min-h-screen w-full justify-center overflow-y-auto rounded-md bg-blue-50 p-4 py-10 text-center text-main-solid md:pb-[6.75rem] md:pt-20">
      <div className="flex flex-col items-center">
        <h2 className="mb-4 max-w-xs font-heading text-xl font-bold">
          There was an issue while loading this page.
        </h2>

        <div className="flex mb-8 gap-4">
          <Button
            variant="outlined"
            onClick={() => {
              window.location.reload();
            }}
          >
            Reload
          </Button>
          <LinkButton href="/dashboard">Go to dashboard</LinkButton>
        </div>

        <Collapsible>
          <CollapsibleTrigger className="mb-4 rounded-md p-2 px-4 text-sm text-blue-800/70 transition duration-300 hover:bg-blue-100">
            View technical details
          </CollapsibleTrigger>

          <CollapsibleContent className="mx-auto max-h-none max-w-xl text-blue-800/70">
            <p className="max-h-64 w-full overflow-auto break-all font-mono">
              {error.message}
            </p>
          </CollapsibleContent>
        </Collapsible>
      </div>
    </div>
  );
}
