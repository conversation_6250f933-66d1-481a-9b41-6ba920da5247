import React, { useState, useEffect, useRef } from 'react';
import * as Dialog from '@radix-ui/react-dialog';
import { But<PERSON>, DialogHeader } from '@/components/core';
import { useQuery } from '@tanstack/react-query';
import Slider from 'react-slick';
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { Spinner } from '@/icons/core';
import { StockProps } from '@/app/(web-store)/[webStoreId]/components/types/products/producttype';
import CloseIcon from '../icons/CloseIcon';
import Image from 'next/image';
import { fetchProdcutsDetails } from '@/app/(web-store)/[webStoreId]/components/api/products/fetchProductDetails';
import { CustomNextArrow, CustomPrevArrow } from '../util/SliderArrows';
import useCartStore, { CartItem } from '@/app/(web-store)/[webStoreId]/components/store/cartStore';
import { CartData } from '@/app/(web-store)/[webStoreId]/components/mock/cartData';
import { Item } from '@/app/(web-store)/[webStoreId]/components/types/products/productDetailsTypes';
import MinusIcon from '@/app/(web-store)/[webStoreId]/components/icons/MinusIcon';
import AddIcon from '@/app/(web-store)/[webStoreId]/components/icons/AddIcon';
import DotPaging from '../util/SliderDot';
import ProductImagePreview from './ProductImagePreview';
// import CheckoutDetails from '../cart/CheckoutDetails';

interface Prop {
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    open: boolean;
    product: StockProps | undefined;
    id: string;
    setShowCheckoutModal: React.Dispatch<React.SetStateAction<boolean>>
    storeId: string;
    showCheckoutModal: boolean
}

const ProductDetails = ({ open, setOpen, product, id, storeId, setShowCheckoutModal }: Prop) => {
    const productId = String(product?.item);
    // const [productDetailId, setProductDetailId] = useState(productId)
    const addToCart = useCartStore(state => state.addToCart);
    const updateCart = useCartStore(state => state.updateCart);
    const deleteFromCart = useCartStore(state => state.deleteFromCart);
    const cart = useCartStore((state) => state.carts[storeId]);
    const [cartDataArray, setCartDataArray] = useState<CartItem[]>(CartData);
    const sliderRef = useRef<Slider | null>(null);  // Use
    const [imgPreviewSrc, setImgPreviewSrc] = useState("")
    const [showImagePreview, setShowImagePreview] = useState(false)

    const { data, isLoading } = useQuery({
        queryFn: () => fetchProdcutsDetails(id, product?.item as string),
        queryKey: ["product-details", id, product?.item],
    });

    const images = data?.data?.items[0]?.images?.filter(image => image !== null) as string[]; // Filter out null images
    // console.log(images);

    const settings = {
        dots: false,
        infinite: false,
        arrows: true,
        prevArrow: <CustomPrevArrow />,  // Custom previous arrow
        nextArrow: <CustomNextArrow />,  // Custom next arrow
        speed: 500,
        slidesToShow: 1,
        slidesToScroll: 1,
        autoplay: true,
        autoplaySpeed: 3000,

    };




    useEffect(() => {
        setCartDataArray(cart);
    }, [cart]);

    const isInCart = cartDataArray?.some(cart => cart.id === productId);

    const addItemToCart = (productItem: Item | undefined) => {
        if (!productItem) return;

        const newCartItem = {
            id: productId,
            branch_id: String(product?.branch),
            company_id: id,
            product_img: productItem?.images[0],
            product_name: productItem?.name,
            product_description: String(productItem?.product_description),
            price: Number(productItem.selling_price),
            quantity: 1,
            subTotal: Number(productItem.selling_price),
        };

        addToCart(storeId, [newCartItem]);
    };



    const handleQuantityChange = (productId: string, change: number) => {
        const currentCartItem = cart?.find(item => item?.id === productId);
        if (!currentCartItem) return;

        const newQuantity = Math.max(0, currentCartItem?.quantity + change); // Ensure quantity doesn't drop below 0

        if (newQuantity <= 0) {
            deleteFromCart(storeId, productId); // Remove item from cart if quantity reaches 0
        } else {
            updateCart(storeId, productId, newQuantity);
        }
    };


    const handleDotClick = (index: number) => {
        if (sliderRef.current) {
            sliderRef.current.slickGoTo(index);  // Go to the clicked slide
        }
    };
    return (
        <div className={`w-full z-[9999] `}>
            <Dialog.Root open={open}>
                <Dialog.Portal>
                    <Dialog.Overlay className="fixed inset-0  z-[9999999]  bg-black/80 backdrop-blur-0 transition-all duration-100 data-[state=closed]:animate-out data-[state=closed]:fade-out data-[state=open]:fade-in" />
                    <Dialog.Content className={` w-[100%] lg:max-w-[50rem] z-[999999999999] fixed left-[50%] max-md:bottom-0 md:top-[50%] md:max-h-[25rem] h-[98vh] overflow-y-auto md:h-[30rem] translate-x-[-50%] md:translate-y-[-50%] rounded-[1.25rem] bg-white dark:bg-[#0D0D0D]  shadow-[hsl(206_22%_7%_/_35%)_0px_10px_38px_-10px,_hsl(206_22%_7%_/_20%)_0px_10px_20px_-15px] animate-in focus:outline-none`}>
                        <DialogHeader className="py-[1.25rem] px-4 md:px-[1.5rem] bg-white dark:bg-[#0D0D0D] border-opacity-20 border-b-[.0187rem] border-black z-[9999] sticky top-0">
                            <Dialog.Title className='text-[#242424] text-xs md:text-base font-semibold dark:text-white'>Product details</Dialog.Title>
                            <Dialog.Close className="flex justify-center h-[1.625rem] w-[1.625rem] bg-[#EFEFEF] rounded-full items-center" onClick={() => setOpen(false)}>
                                <CloseIcon />
                            </Dialog.Close>
                        </DialogHeader>
                        {
                            isLoading ? <div className='w-full h-full flex justify-center items-center bg-white'><Spinner className='animate-spin' color='blue' /></div> :
                                <div className="grid grid-cols-1 md:grid-cols-2 w-full gap-[1.8125rem] py-3 justify-center items-center p-4">
                                    <Slider {...settings} ref={sliderRef}>
                                        {images?.map((image, index) => (
                                            <div className="h-[19rem] relative cursor-pointer " key={index} onClick={() => {
                                                setShowImagePreview(true)
                                                setImgPreviewSrc(image)
                                            }}>
                                                {/* Image */}
                                                <Image
                                                    alt={image}
                                                    className="rounded-[1.25rem]"
                                                    layout="fill"
                                                    objectFit="cover"
                                                    src={image}
                                                />

                                                {/* Overlay */}
                                                <div className="absolute inset-0 bg-black opacity-30 rounded-lg"></div>

                                                {/* Optional Content on Top of the Overlay */}
                                                <div className="absolute inset-0 flex items-center justify-center text-white text-lg font-semibold">
                                                    {/* Add any content you want to display over the image, e.g., product name */}

                                                </div>
                                            </div>
                                        ))}
                                    </Slider>
                                    <div className="mt-4 flex md:hidden space-x-2 relative">
                                        {images?.map((_, index: number) => (
                                            <DotPaging images={images} index={index} key={index} onClick={handleDotClick} />
                                        ))}
                                    </div>

                                    <div className=" h-full w-full">
                                        <h2 className='text-lg font-semibold'>{data?.data?.items[0]?.name}</h2>
                                        <p className='mt-2 text-xs text-black text-opacity-70'>{data?.data?.items[0]?.product_description}</p>
                                        <div className="mt-5">
                                            <p className='text-black text-xl font-bold font-sans'>&#8358;{Number(data?.data?.items[0]?.selling_price).toLocaleString()}</p>

                                            <div className="flex justify-between mt-[2rem] flex-col gap-5 md:flex-row md:items-center w-full">
                                                <div className=" flex items-center gap-6">

                                                    {!isInCart ? (
                                                        <Button
                                                            className='sm:px-[1.6875rem] p-3 md:py-[0.8rem]  bg-[#EFEFEF] font-medium text-xs md:text-xs  text-black rounded-10'
                                                            // variant={"outlined"}
                                                            onClick={() => addItemToCart(data?.data?.items[0])}
                                                        >
                                                            Add to cart
                                                        </Button>
                                                    ) : (
                                                        <div className='flex border border-[#E4E7E9] rounded items-center p-2 md:px-4 md:py-2 gap-x-[.2813rem]'>
                                                            <Button className='p-1 bg-[#F9F9F9] w-[2.625rem] h-[1.8rem] md:h-[2.1875rem] rounded-lg flex justify-center items-center' onClick={() => handleQuantityChange(String(productId), -1)}>
                                                                <MinusIcon className='w-[10px] md:w-4' fill='white' />
                                                            </Button>
                                                            <input
                                                                className='max-w-[2rem] md:max-w-[3.5rem] bg-transparent outline-none border-none text-center text-base text-black dark:text-white'
                                                                type='number'
                                                                value={cartDataArray.find(cart => cart.id === productId)?.quantity}
                                                                readOnly
                                                            />
                                                            <Button className='p-1 bg-[#F9F9F9] w-[2.625rem] h-[1.8rem] md:h-[2.1875rem] rounded-lg flex justify-center items-center' onClick={() => handleQuantityChange(String(productId), 1)}>
                                                                <AddIcon className='w-[10px] md:w-4' fill='white' />
                                                            </Button>
                                                        </div>
                                                    )}

                                                    <Button className='p-3 md:px-[2.25rem]  md:py-[0.8rem] bg-black text-xs md:text-xs font-medium  text-white rounded-[.625rem]'
                                                        onClick={() => {
                                                            !isInCart && addItemToCart(data?.data?.items[0])
                                                            setShowCheckoutModal(true)
                                                            setOpen(false)
                                                        }}>Buy Now</Button>
                                                </div>
                                            </div>
                                            <div className="mt-4 hidden md:flex space-x-2 relative">
                                                {images?.map((_, index: number) => (
                                                    <DotPaging images={images} index={index} key={index} onClick={handleDotClick} />
                                                ))}

                                            </div>
                                        </div>
                                    </div>
                                </div>
                        }
                    </Dialog.Content>
                </Dialog.Portal>
            </Dialog.Root>

            {showImagePreview && <ProductImagePreview imgSrc={imgPreviewSrc} open={showImagePreview} setOpen={setShowImagePreview} />}


        </div>

    );
};

export default ProductDetails;
