import Axios, { type AxiosInstance } from 'axios';

const AUTH_API_BASE_URL = process.env.NEXT_PUBLIC_API_AUTH_BASE_URL as string;

const MANAGEMENT_API_BASE_URL = process.env
  .NEXT_PUBLIC_API_SPEND_MANAGEMENT_BASE_URL as string;

//TODO IMPLEMENT AND IMPROVED WAY TO MANAGE THIS STRUCTURE
// IMPLEMENT LATER
export const authAxios = Axios.create({
  baseURL: AUTH_API_BASE_URL,
});

export const managementAxios = Axios.create({
  baseURL: MANAGEMENT_API_BASE_URL,
});

export const setAxiosDefaultToken = (
  token: string,
  axiosInstance: AxiosInstance
) => {
  axiosInstance.defaults.headers.common.Authorization = `Bearer ${token}`;
};

export const deleteAxiosDefaultToken = () => {
  delete authAxios.defaults.headers.common.Authorization;
};
