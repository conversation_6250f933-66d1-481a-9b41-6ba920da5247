import { Headerimages } from '@/app/(web-store)/[webStoreId]/components/api/store/fetchStore';
import Image from 'next/image'
import React from 'react'
import EmailIcon from '../icons/EmailIcon';
import CallIcon from '../icons/CallIcon';
import LocationIcon from '../icons/LocationIcon';
import FacebookIcon from '../icons/FacebookIcon';
// import { LinkButton } from '@/components/core';
import WhatsappIcon from '../icons/WhatsaapIcon';
import InstagramIcon from '../icons/InstagramIcon';
import XIcon from '../icons/XIcon';
import PayboxIcon from '../icons/PayboxIcon';
interface Prop {
    header_images: Headerimages;
    header_logo_text: string;
    header_logos: Headerimages;
    header_description: string;
    contact_address: string;
    contact_description: string;
    contact_email: string;
    contact_phone_number: string;
    contact_visible: boolean
    whatsapp_phone_number: string
    x_link: string;
    facebook_link: string
    instagram_link: string;
    whatsapp_url: string;

}
const Footer = ({ header_description, header_logo_text, header_logos, whatsapp_url, contact_email, contact_address, contact_phone_number, facebook_link, instagram_link, x_link, whatsapp_phone_number }: Prop) => {
    return (
        <div className='py-16 w-full grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-[1.5fr_1fr_1fr] px-6 gap-y-3 sm:px-[2rem] lg:px-[5.75rem] items-center bg-black'>
            <div className="w-full">
                <div className=" items-center grid gap-3 grid-cols-1 md:grid-cols-[1.4fr_1fr]">
                    <div className="flex gap-[.7738rem] ">
                        <div className="">
                            <Image
                                alt='logo'
                                height={48}
                                src={header_logos?.url}
                                width={48}
                            />

                        </div>
                        <div className=""><h2 className='font-semibold text-base md:text-[1.375rem] text-white font-outfit'>{header_logo_text}</h2>
                            <p className='text-[#AAAAAA] text-xxs md:text-xs font-sans'>{header_description}</p></div>
                    </div>
                    <div className="flex items-center gap-3">
                        <div className="w-8 h-8 shrink-0 bg-[#FFFFFF33] flex justify-center items-center rounded-full"><EmailIcon /></div>
                        <p className='text-white'>{contact_email}</p>
                    </div>

                </div>
                <div className=" items-center gap-3 grid grid-cols-1 md:grid-cols-[1.4fr_1fr] mt-3 md:mt-14">
                    <div className="flex items-center gap-3">
                        <div className="w-8 h-8 shrink-0 bg-[#FFFFFF33] flex justify-center items-center rounded-full"><CallIcon /></div>
                        <p className='text-white'>{contact_phone_number}</p>
                    </div>
                    <div className="flex items-center gap-3">
                        <div className="w-8 h-8 shrink-0 bg-[#FFFFFF33] flex justify-center items-center rounded-full"><LocationIcon /></div>
                        <p className='text-white'>{contact_address}</p>
                    </div>

                </div>
            </div>
            <div className="hidden xl:block"></div>
            <div className="">
                <div className="flex items-center gap-[.875rem]">
                    {facebook_link && <a className='bg-transparent p-0' href={`${facebook_link}`} target='_blank'>
                        <div className="w-[2.875rem] rounded-full bg-[#FFFFFF33] flex justify-center items-center h-[2.875rem]" >
                            <FacebookIcon />
                        </div>
                    </a>}
                    {whatsapp_phone_number && <a className='bg-transparent p-0' href={`${whatsapp_url}`} target='_blank'>
                        <div className="w-[2.875rem] rounded-full bg-[#FFFFFF33] flex justify-center items-center h-[2.875rem]" >
                            <WhatsappIcon />
                        </div>
                    </a>}
                    {instagram_link && <a className='bg-transparent p-0' href={`${instagram_link}`} target='_blank'>
                        <div className="w-[2.875rem] rounded-full bg-[#FFFFFF33] flex justify-center items-center h-[2.875rem]" >
                            <InstagramIcon />
                        </div>
                    </a>}
                    {x_link && <a className='bg-transparent p-0' href={`${x_link}`} target='_blank'>
                        <div className="w-[2.875rem] rounded-full bg-[#FFFFFF33] flex justify-center items-center h-[2.875rem]" >
                            <XIcon />
                        </div>
                    </a>}
                </div>
                <div className="flex items-center gap-2  mt-[3.625rem] md:mt-[1.9375rem]">
                    <div className="">
                        <PayboxIcon />
                    </div>
                    <div className="">
                        <p className='text-xxs text-white text-opacity-50'>Powered by:</p>
                        <p className='text-xxl text-white font-bold'>Paybox360</p>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default Footer