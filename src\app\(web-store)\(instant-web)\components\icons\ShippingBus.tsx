import * as React from "react";
import { SVGProps } from "react";

interface SVGComponentProps extends SVGProps<SVGSVGElement> {
    width?: number | string;
    height?: number | string;
    fill?: string;
}

const ShippingBus: React.FC<SVGComponentProps> = ({
    width = 20,
    height = 20,
    fill = "#292D32",
    ...props
}) => (
    <svg
        fill="none"
        height={height}
        viewBox="0 0 20 20"
        width={width}
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path
            d="M10.7621 12.0943H10.0002C9.68782 12.0943 9.42877 11.8353 9.42877 11.5229C9.42877 11.2105 9.68782 10.9515 10.0002 10.9515H10.7621C11.2878 10.9515 11.7145 10.5248 11.7145 9.99907V2.95145H5.42877C4.52973 2.95145 3.70685 3.43906 3.26494 4.22382C3.11256 4.4981 2.76212 4.59718 2.48783 4.4448C2.21355 4.29242 2.11447 3.94193 2.26685 3.66764C2.90685 2.52479 4.1183 1.80859 5.42877 1.80859H12.2859C12.5983 1.80859 12.8573 2.06764 12.8573 2.38002V9.99907C12.8573 11.1572 11.9202 12.0943 10.7621 12.0943Z"
            fill={fill}
        />
        <path
            d="M15.3344 16.6662H14.5725C14.2601 16.6662 14.001 16.4071 14.001 16.0947C14.001 15.569 13.5744 15.1423 13.0486 15.1423C12.5229 15.1423 12.0963 15.569 12.0963 16.0947C12.0963 16.4071 11.8372 16.6662 11.5248 16.6662H8.47721C8.16483 16.6662 7.90579 16.4071 7.90579 16.0947C7.90579 15.569 7.47912 15.1423 6.9534 15.1423C6.42769 15.1423 6.00102 15.569 6.00102 16.0947C6.00102 16.4071 5.74198 16.6662 5.42959 16.6662H4.66769C3.09055 16.6662 1.81055 15.3862 1.81055 13.809C1.81055 13.4966 2.06959 13.2376 2.38198 13.2376C2.69436 13.2376 2.9534 13.4966 2.9534 13.809C2.9534 14.7538 3.72293 15.5233 4.66769 15.5233H4.93434C5.18577 14.6471 5.9934 13.9995 6.9534 13.9995C7.9134 13.9995 8.72104 14.6471 8.97247 15.5233H11.0372C11.2886 14.6471 12.0963 13.9995 13.0563 13.9995C14.0163 13.9995 14.8239 14.6471 15.0753 15.5233H15.3344C16.2791 15.5233 17.0486 14.7538 17.0486 13.809V12.0947H15.3344C14.6029 12.0947 14.001 11.4928 14.001 10.7614V8.47568C14.001 7.74425 14.5953 7.14235 15.3344 7.14235L14.5191 5.71759C14.3515 5.42044 14.0315 5.23758 13.6887 5.23758H12.8582V9.99949C12.8582 11.1576 11.921 12.0947 10.7629 12.0947H10.001C9.68864 12.0947 9.42959 11.8357 9.42959 11.5233C9.42959 11.2109 9.68864 10.9519 10.001 10.9519H10.7629C11.2886 10.9519 11.7153 10.5252 11.7153 9.99949V4.66616C11.7153 4.35377 11.9744 4.09473 12.2867 4.09473H13.6887C14.4429 4.09473 15.1362 4.49855 15.5096 5.15379L16.8125 7.43187C16.9115 7.60711 16.9115 7.82806 16.8125 8.0033C16.7134 8.17854 16.5229 8.2852 16.3172 8.2852H15.3344C15.2277 8.2852 15.1439 8.36901 15.1439 8.47568V10.7614C15.1439 10.8681 15.2277 10.9519 15.3344 10.9519H17.6201C17.9325 10.9519 18.1915 11.2109 18.1915 11.5233V13.809C18.1915 15.3862 16.9115 16.6662 15.3344 16.6662Z"
            fill={fill}
        />
        <path
            d="M6.95266 18.1895C5.79456 18.1895 4.85742 17.2524 4.85742 16.0943C4.85742 14.9362 5.79456 13.999 6.95266 13.999C8.11076 13.999 9.0479 14.9362 9.0479 16.0943C9.0479 17.2524 8.11076 18.1895 6.95266 18.1895ZM6.95266 15.1419C6.42695 15.1419 6.00028 15.5685 6.00028 16.0943C6.00028 16.62 6.42695 17.0466 6.95266 17.0466C7.47837 17.0466 7.90504 16.62 7.90504 16.0943C7.90504 15.5685 7.47837 15.1419 6.95266 15.1419Z"
            fill={fill}
        />
        <path
            d="M13.0484 18.1895C11.8903 18.1895 10.9531 17.2524 10.9531 16.0943C10.9531 14.9362 11.8903 13.999 13.0484 13.999C14.2065 13.999 15.1436 14.9362 15.1436 16.0943C15.1436 17.2524 14.2065 18.1895 13.0484 18.1895ZM13.0484 15.1419C12.5226 15.1419 12.096 15.5685 12.096 16.0943C12.096 16.62 12.5226 17.0466 13.0484 17.0466C13.5741 17.0466 14.0008 16.62 14.0008 16.0943C14.0008 15.5685 13.5741 15.1419 13.0484 15.1419Z"
            fill={fill}
        />
    </svg>
);

export default ShippingBus;
