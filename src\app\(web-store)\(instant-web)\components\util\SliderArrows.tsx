
interface Prop {
    className?: string;
    style?: React.CSSProperties;
    onClick?: React.MouseEventHandler<HTMLDivElement>;
}
export const CustomPrevArrow = (props: Prop) => {
    const { className, style, onClick } = props;
    return (
        <div
            className={className}
            style={{ ...style, display: "block", left: "10px", zIndex: 1 }}
            onClick={onClick}
        >
            <svg fill="none" height="32" viewBox="0 0 32 32" width="32" xmlns="http://www.w3.org/2000/svg">
                <circle cx="16" cy="16" fill="white" r="16" />
                <path d="M18 11L13 16L18 21" stroke="black" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" />
            </svg>

        </div>
    );
};

export const CustomNextArrow = (props: Prop) => {
    const { className, style, onClick } = props;
    return (
        <div
            className={className}
            style={{ ...style, display: "block", right: "20px", zIndex: 1 }}
            onClick={onClick}
        >
            <svg fill="none" height="32" viewBox="0 0 32 32" width="32" xmlns="http://www.w3.org/2000/svg">
                <circle cx="16" cy="16" fill="white" r="16" transform="matrix(-1 0 0 1 32 0)" />
                <path d="M14 11L19 16L14 21" stroke="black" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" />
            </svg>

        </div>
    );
};