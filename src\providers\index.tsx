'use client';

import { SessionProvider, useSession } from 'next-auth/react';
import * as React from 'react';
import { Provider as WrapBalancerProvider } from 'react-wrap-balancer';

import { setMultipleAxiosDefaultTokens } from '@/lib/apiClient';
import { ReactQueryProvider } from '@/lib/reactQuery';
import { checkIsIOS, disableIOSTextFieldZoom } from '@/utils/inputs';
import { managementAxios, setAxiosDefaultToken } from '@/lib/axios';

interface ClientAxiosTokenSetterProps {
  children: React.ReactNode;
}

export function ClientAxiosTokenSetter({
  children,
}: ClientAxiosTokenSetterProps) {
  const { data: session } = useSession();
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  //@ts-ignore
  const token = session?.user?.access;


  React.useEffect(() => {
    if (token) {
      setMultipleAxiosDefaultTokens(token);
      setAxiosDefaultToken(token, managementAxios)
    }
  }, [token]);


  return <>{children}</>;
}

interface ProvidersProps {
  children: React.ReactNode;
}

export function Providers({ children }: ProvidersProps) {
  React.useEffect(() => {
    // Check if the current device is iOS and disable text field zooming.
    if (checkIsIOS()) {
      disableIOSTextFieldZoom();
    }
  }, []);

  return (
    <SessionProvider>
      <ReactQueryProvider>
        <ClientAxiosTokenSetter>
          <WrapBalancerProvider>{children}</WrapBalancerProvider>
        </ClientAxiosTokenSetter>
      </ReactQueryProvider>
    </SessionProvider>
  );
}
