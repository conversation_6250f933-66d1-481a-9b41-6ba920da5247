import './globals.css';

import { DM_Sans, <PERSON>ra, Wix_Madefor_Display } from 'next/font/google';
import localFont from 'next/font/local';
import Script from 'next/script';
import { Toaster } from 'react-hot-toast';

import { Providers } from '@/providers';
import { cn } from '@/utils/classNames';

const fontSans = DM_Sans({
  subsets: ['latin'],
  variable: '--font-sans',
  display: 'swap',
  adjustFontFallback: false,
  fallback: [
    'DM_Sans',
    'ui-sans-serif',
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'Noto Sans',
    'sans-serif',
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Noto Color Emoji',
  ],
});

const fontHeading = Sora({
  subsets: ['latin'],
  variable: '--font-heading',
  display: 'swap',
  adjustFontFallback: false,
  fallback: [
    'Sora',
    'ui-sans-serif',
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'Noto Sans',
    'sans-serif',
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Noto Color Emoji',
  ],
});

const fontWixDisplay = Wix_Madefor_Display({
  subsets: ['latin'],
  variable: '--font-wix-display',
  display: 'swap',
  adjustFontFallback: false,
  fallback: [
    'ui-sans-serif',
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'Noto Sans',
    'sans-serif',
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Noto Color Emoji',
  ],
});

const fontClash = localFont({
  src: './fonts/ClashDisplay-Variable.woff2',
  variable: '--font-clash',
  display: 'swap',
});
const fontOutfit = localFont({
  src: './fonts/static/Outfit-SemiBold.ttf',
  variable: '--font-outfit',
  display: 'swap',
});

export const metadata = {
  title: 'Paybox 360',
  description:
    'Unlock the simplest of solutions to manage your business and spending.',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        {/* <!-- Google Tag Manager --> */}
        <Script id="google-tag-manager" strategy="afterInteractive">
          {`
        (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','${'GTM-KNVJB9N7'}');
      `}
        </Script>

        {/* <!-- Hotjar Tracking Code for Paybox360 --> */}
        <Script
          dangerouslySetInnerHTML={{
            __html: `(function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:3725464,hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');`,
          }}
          id="show-banner"
        />

        {/* <!-- PROVESRC Tracking Code for Paybox360 --> */}
        <Script
          dangerouslySetInnerHTML={{
            __html: `!function(o,i){window.provesrc && window.console && console.error && console.error("ProveSource is included twice in this page."), provesrc = window.provesrc = { dq: [], display: function () { this.dq.push(arguments) } }, o._provesrcAsyncInit = function () { provesrc.init({ apiKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************.JxvE_IUJYd8TBBkEzbgOd63LpPgOfYF-O6KhY222RVI", v: "0.0.4" }) };var r=i.createElement("script");r.type="text/javascript",r.async=!0,r["ch"+"ar"+"set"]="UTF-8",r.src="https://cdn.provesrc.com/provesrc.js";var e=i.getElementsByTagName("script")[0];e.parentNode.insertBefore(r,e)}(window,document);`,
          }}
          id="provesrc-banner"
        />

        <link href="/favicon.ico" rel="icon" sizes="any" />
      </head>
      <body
        className={cn(
          'font-sans text-dark-text',
          fontOutfit.variable,
          fontSans.variable,
          fontHeading.variable,
          fontWixDisplay.variable,
          fontClash.variable,
        )}
      >
        <Toaster
          containerStyle={{
            zIndex: 99999,
          }}
          position="top-center"
          toastOptions={{
            style: {
              zIndex: 99999,
            },
          }}
        />

        <Providers>{children}</Providers>

        {/* <!-- FRESH CHAT for Paybox360 --> */}
        <Script
          dangerouslySetInnerHTML={{
            __html: `function initFreshChat() {
            window.fcWidget.init({
                token: "dbe4f05b-6869-4ce0-8fe3-450dcca6bb4d",
          host: "https://libertypay.freshchat.com",
          widgetUuid: "79d6ccd5-7d8f-48e3-a8e3-9286ae22c53c"
            });
          }
          function initialize(i,t){var e;i.getElementById(t)?
          initFreshChat():((e=i.createElement("script")).id=t,e.async=!0,
          e.src="https://libertypay.freshchat.com/js/widget.js",e.onload=initFreshChat,i.head.appendChild(e))}
          function initiateCall(){initialize(document,"Freshchat-js-sdk")}
          window.addEventListener?window.addEventListener("load",initiateCall,!1):
          window.attachEvent("load",initiateCall,!1);`,
          }}
          id="chat-icon-freshworks"
        />

        {/* <!-- FRESH DESK EMAIL for Paybox360 --> */}
        <Script
          dangerouslySetInnerHTML={{
            __html: `window.fwSettings={
            'widget_id':73000003217
          	};
          !function(){if("function"!=typeof window.FreshworksWidget){var n=function(){n.q.push(arguments)};n.q=[],window.FreshworksWidget=n}}()`,
          }}
          id="chat-email-icon-freshworks"
        />
        <Script
          dangerouslySetInnerHTML={{
            __html: `src='https://widget.freshworks.com/widgets/73000003217.js' async defer`,
          }}
          id="chat-email-widget-icon-freshworks"
        />

        <noscript
          dangerouslySetInnerHTML={{
            __html: `<iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KNVJB9N7" height="0" width="0" style="display: none; visibility: hidden;" />`,
          }}
        />
      </body>
    </html>
  );
}
