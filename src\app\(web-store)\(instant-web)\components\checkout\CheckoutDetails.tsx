import React, { useState } from 'react';
import * as Dialog from '@radix-ui/react-dialog';


import CloseIcon from '../icons/CloseIcon';
import {
    DialogHeader, ErrorModal, Button,
    Command,
    // CommandEmpty,
    // CommandInput,
    CommandItem,
    CommandList,
} from '@/components/core';
import useCartStore from '@/app/(web-store)/[webStoreId]/components/store/cartStore';
import FullScreenIcon from '../icons/FullScreenIcon';
import BasketIcon from '../icons/BasketIcon';
import ShippingBus from '../icons/ShippingBus';
import CardIcon from '../icons/CardIcon';
import { Popover, PopoverTrigger, PopoverContent } from '@radix-ui/react-popover';
import { z } from 'zod';
import { Controller, useForm, useWatch } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Country, ICountry, IState, State } from 'country-state-city';
import { checkSuccessProp } from '@/app/(web-store)/[webStoreId]/components/checkout/types';
import DebounceInput from '../util/DebounceInput';
import CaretDownIcon from '../icons/CaretDown';
import { contactInfoPropTypes } from '@/app/(web-store)/[webStoreId]/components/checkout/BillingDetails';
import { useCheckOutProduct } from '@/app/(web-store)/[webStoreId]/components/api/checkout/checkout';
import { useRouter } from 'next/navigation';
// import toast from 'react-hot-toast';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { AxiosError } from 'axios';
import { useErrorModalState } from '@/hooks';
import { SmallSpinner } from '@/icons/core';
import TransferCheckoutModal from './TransferModal';
// import CheckoutSuccess from './CheckoutSuccess';

interface Prop {
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    open: boolean;
    setShowCart: React.Dispatch<React.SetStateAction<boolean>>
    // darkMode: string | boolean;
    storeId: string;
    setShowSuccessModal: React.Dispatch<React.SetStateAction<boolean>>;
    setOrderId: React.Dispatch<React.SetStateAction<string>>;
    CheckoutDetails: {
        first_nmae: string;
        last_name: string;
        phone_number: string;
    }
}

const contactSchema = z.object({
    buyer: z.object({
        first_name: z.string().min(1, { message: "Enter first name" }),
        last_name: z.string().min(1, { message: "Enter last name" }),
        middle_name: z.string(),
        address: z.string().min(1, { message: "Enter address" }),
        country: z.string(),
        state: z.string(),
        postal_code: z.string().optional(),
        city: z.string().min(1, { message: "Enter city" }),
        email: z.string().email().min(1, { message: "Enter email" }),
        addition_information: z.string().optional(),
        phone_number: z.string({ required_error: "Enter phone_number." }).min(11, { message: "Enter valid phone_number" }).max(11, { message: "Enter valid phone_number" }),
        ship_to_different_address: z.boolean(),
        paymentOption: z.enum(['CARD', 'TRANSFER', 'DELIVERY', 'USSD', 'CASH']),
    })
});


export type contactInfoProps = z.infer<typeof contactSchema>;

const CheckoutDetails = ({ open, setOpen, storeId, setShowCart, setShowSuccessModal, setOrderId, CheckoutDetails }: Prop) => {
    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();
    const carts = useCartStore((state) => state.carts[storeId]);
    const [data] = useState(carts);
    const totalQuantity = data?.reduce((sum, item) => sum + item.quantity, 0);

    const router = useRouter()
    const cartData = useCartStore((state) => state.carts[storeId]);
    const removeCart = useCartStore((state) => state?.removeCart)
    const [openTransferModal, setOpenTransferModal] = useState(false)
    const [countryList, setCountryList] = React.useState<ICountry[]>([]);
    const [stateList, setStateList] = React.useState<IState[]>([]);
    const [countryCode, setCountryCode] = React.useState('');
    const [searchCountry, setSearchCountry] = React.useState('');
    const [searchState, setSearchState] = React.useState('');
    // const [openUssdModal, setOpenUssdModal] = useState(false)
    // const [checkoutResponse, setCheckoutResponse] = useState<checkSuccessProp>()
    const [transferData, setTransferData] = useState<{
        amount: string,
        bank_name: string;
        bank_code: number;
        account_name: string;
        account_number: string;
        contact_phone_number: string;
        company_name: string;
        branchId: string;
        orderId: string;
        companyId: string;
    }>()


    const totalSum = data.reduce((sum, item) => sum + item.subTotal, 0);

    // Example values for shipping, discount, and tax
    const shipping = 0; // Free shipping
    const discount = 0; // Example discount value
    const tax = 0; // Example tax value

    // Calculate the grand total
    const grandTotal = totalSum + shipping - discount + tax;

    const {
        control,
        register,
        handleSubmit,
        formState: { errors },
    } = useForm<contactInfoProps>({
        resolver: zodResolver(contactSchema),
        defaultValues: {
            buyer: {
                first_name: CheckoutDetails?.first_nmae || "",
                middle_name: "",
                last_name: CheckoutDetails?.last_name || "",
                country: "",
                address: "",
                state: "",
                postal_code: "",
                city: "",
                email: "",
                phone_number: CheckoutDetails?.phone_number || "",
                ship_to_different_address: false,
                paymentOption: "CARD",
                addition_information: ""
            }
        },
        mode: "onChange",
    });


    React.useEffect(() => {
        setCountryList(Country.getAllCountries());
    }, []);

    const countryOptions = countryList
        ?.filter(country => country.name.toLowerCase().includes(searchCountry.toLowerCase()))
        ?.map(country => ({
            value: country.name,
            label: country.name,
            code: country.isoCode,
        }));

    const stateOptions = stateList
        ?.filter(state => state.name.toLowerCase().includes(searchState.toLowerCase()))
        ?.map(state => ({
            value: state.name,
            label: state.name,
            code: state.isoCode,
        }));

    const watchCountry = useWatch({ name: "buyer.country", control });

    React.useEffect(() => {
        const selectedCountry = countryOptions?.find(country => country.value === watchCountry);
        if (selectedCountry) {
            setCountryCode(selectedCountry.code);
        }
    }, [watchCountry, countryOptions]);

    React.useEffect(() => {
        setStateList(State.getStatesOfCountry(countryCode));
    }, [countryCode]);

    const [isCountryPopoverOpen, setIsCountryPopoverOpen] = useState<boolean>(false);
    const [isStatePopoverOpen, setIsStatePopoverOpen] = useState<boolean>(false);



    const { mutate: handleCheckout, isLoading } = useCheckOutProduct()
    const onSubmit = (data: contactInfoPropTypes) => {


        handleCheckout({
            buyer: data,
            cart: cartData,
            discount,
            shipping,
            tax,
            total_price: grandTotal

        }, {
            // onSuccess: (data: CheckoutTypes) => {
            onSuccess: (response: checkSuccessProp) => {
                if (data?.buyer?.paymentOption === "TRANSFER") {
                    setTransferData({
                        account_name: response?.payment_details?.account_name,
                        account_number: response?.payment_details?.account_number,
                        bank_code: response?.payment_details?.bank_code,
                        bank_name: response?.payment_details?.bank_name,
                        amount: String(response?.checkout_order?.total_price),
                        contact_phone_number: data?.buyer?.phone_number,
                        company_name: response?.checkout_order?.company,
                        branchId: response?.checkout_order?.branch_id,
                        companyId: response?.checkout_order?.company_id,
                        orderId: response?.checkout_order?.order_id

                    })
                    setOpenTransferModal(true)
                } else if (data?.buyer?.paymentOption === "CARD") {
                    // toast.success("success")
                    // toast.success("success")
                    router.push(`${response?.payment_details?.payment_link}`)
                    // setOpen(false)
                    removeCart(storeId)



                } else if (data?.buyer?.paymentOption === "DELIVERY") {
                    setOpen(false)
                    setOrderId(response?.checkout_order?.order_id)
                    setShowSuccessModal(true)
                    // setCheckoutResponse(response)
                    // setShowDeliveryInvoice(true)
                    // router?.push(`/instant-web/manage-website/select-company`)
                    // setOpen(false)
                    removeCart(storeId)
                }

            },
            onError: (error) => {
                const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                openErrorModalWithMessage(errorMessage);

            }
        })
        // }


    }
    return (
        <div className={`w-full z-[9999] `}>
            <Dialog.Root open={open}>
                <Dialog.Portal>
                    <Dialog.Overlay className="fixed inset-0  z-[9999999]  bg-black/80 backdrop-blur-0 transition-all duration-100 data-[state=closed]:animate-out data-[state=closed]:fade-out data-[state=open]:fade-in" />
                    <Dialog.Content className={`overflow-y-auto fixed right-0 bottom-0   md:top-0   h-[100vh] w-full md:w-[36.375rem] z-[9999999] rounded-lg bg-white dark:bg-[#0D0D0D] shadow-[hsl(206_22%_7%_/_35%)_0px_10px_38px_-10px,_hsl(206_22%_7%_/_20%)_0px_10px_20px_-15px] animate-in focus:outline-none data-[state=open]:fade-in-90 data-[state=open]:slide-in-from-bottom-10`}>
                        <DialogHeader className="py-[1.25rem] px-4 md:px-[1.5rem] bg-white dark:bg-[#0D0D0D] border-opacity-10 border-b-[.0187rem] border-black z-[9999] sticky top-0">
                            <div className="flex justify-between w-full items-center">
                                <div className="flex items-center gap-3">
                                    <p className='font-medium text-lg text-black'>Cart </p>
                                    <div className=" w-[2rem] h-[2rem] rounded-full bg-[#EFEFEF] flex  justify-center items-center">
                                        <p className='text-black text-sm font-semibold'>{totalQuantity}</p>
                                    </div>
                                </div>
                                <div className="flex items-center  gap-3">
                                    <div className="flex justify-center h-8 w-8 bg-[#EFEFEF] rounded-full items-center" onClick={() => {
                                        setOpen(false)
                                        setShowCart(true)
                                    }}>
                                        <Button className='bg-transparent'> <FullScreenIcon /></Button>
                                    </div>
                                    <div className="flex justify-center h-8 w-8 bg-[#EFEFEF] rounded-full items-center" onClick={() => setOpen(false)}>
                                        <Button className='bg-transparent'>  <CloseIcon height={10} width={10} /></Button>
                                    </div>
                                </div>
                            </div>

                        </DialogHeader>
                        <div className=" flex items-center gap-7 p-3 md:p-6">
                            <div className="flex items-center gap-2">
                                <div className="w-[2rem] h-[2rem] rounded-full bg-[#F8F8F8] flex justify-center items-center">
                                    <Button className='bg-transparent'>  <BasketIcon color='black' height={15} width={15} /></Button>
                                </div>
                                <p className='text-[#818181] text-xs md:text-sm'>Cart</p>
                            </div>
                            <div className="flex items-center gap-2">
                                <div className="w-[2rem] h-[2rem] rounded-full bg-black flex justify-center items-center">
                                    <Button className='bg-transparent' >  <ShippingBus fill='white' height={15} width={15} /></Button>
                                </div>
                                <p className='text-[#000] text-xs md:text-sm'>Shipping details</p>
                            </div>
                            <div className="flex items-center gap-2">
                                <div className="w-[2rem] h-[2rem] rounded-full bg-[#F8F8F8] flex justify-center items-center">
                                    <Button className='bg-transparent' >  <CardIcon /></Button>
                                </div>
                                <p className='text-[#818181] text-xs md:text-sm'>Payment</p>
                            </div>
                        </div>
                        <div className="px-6 -mt-3 pb-3">
                            <p className='text-xs font-semibold text-[#818181] '> Shopping Details: Provide shipping & contact info..
                            </p>
                        </div>
                        <form onSubmit={handleSubmit(onSubmit)}>
                            <div className=" h-[40vh] px-6 overflow-y-auto">
                                <div className=" rounded-10   p-4 w-full mt-3 border-[.0187rem]">
                                    <div className="flex gap-3 items-center">
                                        <p className='text-[#080808] text-base'>Delivery billing information </p>
                                    </div>
                                    <div className="flex items-start justify-between gap-4">
                                        <div className="w-full">
                                            <div className={`flex flex-col md:flex-row justify-between gap-4 mt-2`}>
                                                <div className={`w-full flex justify-between items-center border-[0.5px] rounded-10 px-4 h-[2.75rem] ${errors?.buyer?.first_name ? "border border-red-400" : ""}`}>
                                                    <input placeholder='First name' type="text" {...register("buyer.first_name")} className={`bg-transparent pr-4 h-[2.75rem]  w-full text-sm outline-none border-opacity-30 `} />
                                                    <span className='text-[#CDCDCD] text-xxs'>(Required)</span>
                                                </div>
                                            </div>

                                            {errors?.buyer?.first_name && <span className='text-xs font-clash text-red-900 '>{errors?.buyer?.first_name?.message}</span>}

                                        </div>
                                        <div className="w-full">
                                            <div className={`flex flex-col md:flex-row justify-between gap-4 mt-2`}>
                                                <div className={`w-full flex justify-between items-center border-[0.5px] rounded-10 px-4 h-[2.75rem] ${errors?.buyer?.last_name ? "border border-red-400" : ""}`}>
                                                    <input placeholder='Lastname' type="text" {...register("buyer.last_name")} className={`bg-transparent pr-4 h-[2.75rem]  w-full text-sm outline-none border-opacity-30 `} />
                                                    <span className='text-[#CDCDCD] text-xxs'>(Required)</span>
                                                </div>
                                            </div>

                                            {errors?.buyer?.last_name && <span className='text-xs font-clash text-red-900 '>{errors?.buyer?.last_name?.message}</span>}

                                        </div>
                                    </div>
                                    <div className="flex items-start justify-between gap-4 mt-4">
                                        <div className="w-full">
                                            <div className={`flex flex-col md:flex-row justify-between gap-4 mt-2`}>
                                                <div className={`w-full flex justify-between items-center border-[0.5px] rounded-10 px-4 h-[2.75rem] ${errors?.buyer?.email ? "border border-red-400" : ""}`}>
                                                    <input placeholder='Email' type="text" {...register("buyer.email")} className={`bg-transparent pr-4 h-[2.75rem]  w-full text-sm outline-none border-opacity-30 `} />
                                                    <span className='text-[#CDCDCD] text-xxs'>(Required)</span>
                                                </div>
                                            </div>

                                            {errors?.buyer?.email && <span className='text-xs font-clash text-red-900 '>{errors?.buyer?.email?.message}</span>}

                                        </div>
                                        <div className="w-full">
                                            <div className={`flex flex-col md:flex-row justify-between gap-4 mt-2`}>
                                                <div className={`w-full flex justify-between items-center border-[0.5px] rounded-10 px-4 h-[2.75rem] ${errors?.buyer?.phone_number ? "border border-red-400" : ""}`}>
                                                    <input placeholder='phone no' type="text" {...register("buyer.phone_number")} className={`bg-transparent pr-4 h-[2.75rem]  w-full text-sm outline-none border-opacity-30 `}
                                                        onKeyDown={(e) => {
                                                            // Prevent non-numeric keys (e.g., e, +, -, .)
                                                            if (!/[0-9]/.test(e.key) && e.key !== "Backspace" && e.key !== "Delete" && e.key !== "ArrowLeft" && e.key !== "ArrowRight") {
                                                                e.preventDefault();
                                                            }
                                                        }}
                                                    />
                                                    <span className='text-[#CDCDCD] text-xxs'>(Required)</span>
                                                </div>
                                            </div>

                                            {errors?.buyer?.phone_number && <span className='text-xs font-clash text-red-900 '>{errors?.buyer?.phone_number?.message}</span>}

                                        </div>
                                    </div>

                                    <div className="w-full mt-4">
                                        <div className={`flex flex-col md:flex-row justify-between gap-4 mt-2`}>
                                            <div className={`w-full flex justify-between items-center border-[0.5px] rounded-10 px-4 h-[2.75rem] ${errors?.buyer?.address ? "border border-red-400" : ""}`}>
                                                <input placeholder='Address' type="text" {...register("buyer.address")} className={`bg-transparent pr-4 h-[2.75rem]  w-full text-sm outline-none border-opacity-30 `} />
                                                <span className='text-[#CDCDCD] text-xxs'>(Required)</span>
                                            </div>
                                        </div>

                                        {errors?.buyer?.address && <span className='text-xs font-clash text-red-900 '>{errors?.buyer?.address?.message}</span>}

                                    </div>
                                    <div className="grid grid-cols-2  w-full items-start justify-between overflow-x-auto gap-2 md:gap-4 mt-4">

                                        <div className="flex flex-col w-full">
                                            <label className="text-sm font-medium" htmlFor="buyer-country">Country</label>
                                            <Controller
                                                control={control}
                                                name="buyer.country"
                                                render={({ field }) => (
                                                    <Popover open={isCountryPopoverOpen} onOpenChange={setIsCountryPopoverOpen}>
                                                        <PopoverTrigger asChild>
                                                            <Button
                                                                className="w-full flex justify-between items-center h-[2.75rem] border-[0.5px] border-[#e5e5e5] border-opacity-80 text-sm text-[#818181] dark:text-[#DBDBDB] text-left"
                                                                id="buyer-country"  // Add an id to match htmlFor in the label
                                                                variant={"outlined"}
                                                                onClick={() => setIsCountryPopoverOpen(true)}
                                                            >
                                                                {field.value || 'Country'}
                                                                <CaretDownIcon />
                                                            </Button>
                                                        </PopoverTrigger>
                                                        <PopoverContent align="start" className="w-full p-4 shadow-md bg-white !z-[9999999999999999999999999999999]" side="bottom">
                                                            <Command className='relative'>
                                                                <DebounceInput
                                                                    className="bg-white sticky top-0 border-[#e5e5e5] border-[0.5px] mb-2 rounded-md flex"
                                                                    placeholder="Search Country"
                                                                    value={searchCountry ?? ""}
                                                                    onChange={(value) => setSearchCountry(String(value))}
                                                                />
                                                                <CommandList className='max-h-[10vh] overflow-y-auto scrollbar-thin !scrollbar-thumb-gray-400 !scrollbar-track-red-900'>
                                                                    {countryOptions?.map(country => (
                                                                        <CommandItem
                                                                            className='hover:bg-gray-200 hover:py-2 text-sm font-outfit'
                                                                            key={country.code}
                                                                            onSelect={() => {
                                                                                field.onChange(country.value);
                                                                                setSearchCountry('');
                                                                                setIsCountryPopoverOpen(false);
                                                                            }}
                                                                        >
                                                                            {country.label}
                                                                        </CommandItem>
                                                                    ))}
                                                                </CommandList>
                                                            </Command>
                                                        </PopoverContent>
                                                    </Popover>
                                                )}
                                            />
                                        </div>

                                        <div className="flex flex-col w-full">
                                            <label className="text-sm font-medium" htmlFor="buyer.state">State</label>
                                            <Controller
                                                control={control}
                                                name="buyer.state"
                                                render={({ field }) => (
                                                    <Popover open={isStatePopoverOpen} onOpenChange={setIsStatePopoverOpen}>
                                                        <PopoverTrigger asChild>

                                                            <Button className="w-full text-left flex justify-between items-center  h-[2.75rem]  border-[0.5px] border-[#e5e5e5] border-opacity-80  text-sm text-[#818181] dark:text-[#DBDBDB]  " disabled={!countryCode} id="buyer-state"
                                                                variant={"outlined"}
                                                                onClick={() => setIsStatePopoverOpen(true)}>
                                                                {field.value || 'State'}
                                                                <CaretDownIcon />
                                                            </Button>
                                                        </PopoverTrigger>
                                                        <PopoverContent align="start" className="w-full p-4 shadow-md bg-white z-[9999999999999999999999]" side="bottom">
                                                            <Command>

                                                                <DebounceInput
                                                                    className="bg-white border-[#e5e5e5] border-[0.5px] mb-2 rounded-md flex"
                                                                    // placeHolder='Search State'
                                                                    value={searchState ?? ""}
                                                                    onChange={(value) => setSearchState(String(value))}
                                                                />
                                                                <CommandList className='max-h-[10vh] overflow-y-auto  scrollbar-thin !scrollbar-thumb-gray-400 !scrollbar-track-red-900'> {/* Set a maximum height */}

                                                                    {stateOptions?.map(state => (
                                                                        <CommandItem
                                                                            className='hover:bg-gray-200 hover:py-2 text-sm font-outfit' // Add a hover effect if desired

                                                                            key={state.code}
                                                                            onSelect={() => {
                                                                                field.onChange(state.value);
                                                                                setSearchState('');
                                                                                setIsStatePopoverOpen(false)
                                                                            }}
                                                                        >
                                                                            {state.label}
                                                                        </CommandItem>
                                                                    ))}
                                                                </CommandList>
                                                            </Command>
                                                        </PopoverContent>
                                                    </Popover>
                                                )}
                                            />
                                        </div>

                                        {/* <div className="col-span-2 sm:col-span-1 w-full mt-4">
                                            <div className={`flex flex-col md:flex-row justify-between gap-4 mt-2`}>
                                                <div className={`w-full flex justify-between items-center border-[0.5px] rounded-10 px-4 h-[2.75rem] ${errors?.buyer?.city ? "border border-red-400" : ""}`}>
                                                    <input placeholder='City' type="text" {...register("buyer.city")} className={`bg-transparent pr-4 h-[2.75rem]  w-full text-sm outline-none border-opacity-30 `} />
                                                    <span className='text-[#CDCDCD] text-xxs'>(Required)</span>
                                                </div>
                                            </div>

                                            {errors?.buyer?.city && <span className='text-xs font-clash text-red-900 '>{errors?.buyer?.city?.message}</span>}

                                        </div> */}
                                    </div>

                                    <div className="flex items-start justify-between gap-4 mt-3">
                                        <div className="w-full">
                                            <div className={`flex flex-col md:flex-row justify-between gap-4 mt-2`}>
                                                <div className={`w-full flex justify-between items-center border-[0.5px] rounded-10 px-4 h-[2.75rem] ${errors?.buyer?.city ? "border border-red-400" : ""}`}>
                                                    <input placeholder='City' type="text" {...register("buyer.city")} className={`bg-transparent pr-4 h-[2.75rem]  w-full text-sm outline-none border-opacity-30 `} />
                                                    <span className='text-[#CDCDCD] text-xxs'>(Required)</span>
                                                </div>
                                            </div>

                                            {errors?.buyer?.city && <span className='text-xs font-clash text-red-900 '>{errors?.buyer?.city?.message}</span>}

                                        </div>
                                        <div className="w-full">
                                            <div className={`flex flex-col md:flex-row justify-between gap-4 mt-2`}>
                                                <div className={`w-full flex justify-between items-center border-[0.5px] rounded-10 px-4 h-[2.75rem] ${errors?.buyer?.postal_code ? "border border-red-400" : ""}`}>
                                                    <input placeholder='postal code' type="number" {...register("buyer.postal_code")} className={`bg-transparent pr-4 h-[2.75rem]  w-full text-sm outline-none border-opacity-30 `} />
                                                    <span className='text-[#CDCDCD] text-xxs'>(optional)</span>
                                                </div>
                                            </div>

                                            {errors?.buyer?.postal_code && <span className='text-xs font-clash text-red-900 '>{errors?.buyer?.postal_code?.message}</span>}

                                        </div>
                                    </div>


                                    <div className="">

                                    </div>
                                    <div className={`w-full flex flex-col mt-3 py-3  border-[0.5px] rounded-10 px-4 min-h-[2.75rem] ${errors?.buyer?.email ? "border border-red-400" : ""}`}>
                                        <p className='text-sm text-[#818181]'>Additional delivery note</p>
                                        <textarea className={`h-[3.75rem] py-2   w-full outline-none px-3 border border-none dark:text-[#DBDBDB] dark:bg-transparent rounded-[.125rem]  ${errors?.buyer?.addition_information ? "border border-red-400" : ""}`} id='address' placeholder='' {...register("buyer.addition_information")} />

                                    </div>
                                </div>
                            </div>
                            <div className=" h-[40vh] px-6">
                                <div className="p-6">
                                    <p className='text-[#080808] text-lg font-medium'>Payment summary</p>
                                    <div className="flex items-center justify-between mt-1">
                                        <p className='text-[#898989] text-sm'>Subtotal</p>
                                        <h2 className='text-black text-base font-bold font-outfit'>&#8358;{Number(totalSum)?.toLocaleString()}</h2>
                                    </div>
                                    <div className="flex items-center justify-between mt-1">
                                        <p className='text-[#898989] text-sm'>Delivery</p>
                                        <h2 className='text-black text-base font-bold font-outfit'>&#8358;{Number(0)?.toLocaleString()}</h2>
                                    </div>
                                    <div className="flex items-center justify-between mt-1">
                                        <p className='text-[#898989] text-sm'>Tax</p>
                                        <h2 className='text-black text-base font-bold font-outfit'>&#8358;{Number(0)?.toLocaleString()}</h2>
                                    </div>
                                </div>
                                <div className="flex items-center border-t-[0.3px] px-6 py-2 justify-between">
                                    <p className='text-[#080808] text-lg'>Total amount:</p>
                                    <h2 className='text-black text-xl font-bold font-outfit'>&#8358;{Number(grandTotal).toLocaleString()}</h2>
                                </div>
                                <div className="p-3 md:p-6">
                                    <p className='text-[#080808] text-sm font-medium'>Payment method</p>
                                    <div className="">
                                        <div className="flex items-center flex-wrap mt-2 gap-4">

                                            {/* <label className="flex items-center w-full "> */}

                                            <div className="grid grid-cols-3 flex-1 items-center  w-full  mt-2 gap-3">

                                                <label className="flex items-center w-full space-x-2 md:space-x-5 border-[0.5px]  p-[.875rem] rounded-10 ">
                                                    <input
                                                        type="radio"
                                                        {...register('buyer.paymentOption')}
                                                        className="form-radio h-3 w-3 text-indigo-600"
                                                        value="CARD"
                                                    />
                                                    <span className='text-sm text-[#000] dark:text-[#DBDBDB]'> Card</span>
                                                </label>
                                                <label className="flex items-center w-full space-x-2 md:space-x-5 border-[0.5px] p-[.875rem] rounded-10 ">
                                                    <input
                                                        type="radio"
                                                        {...register('buyer.paymentOption')}
                                                        className="form-radio h-3 w-3 shrink-0 text-indigo-600"
                                                        value="TRANSFER"
                                                    />
                                                    <span className='text-sm text-[#242424] dark:text-[#DBDBDB]'>Transfer</span>
                                                </label>
                                                <label className="flex items-center w-full space-x-2 md:space-x-5 border-[0.5px] p-[.875rem] rounded-10 ">
                                                    <input
                                                        type="radio"
                                                        {...register('buyer.paymentOption')}
                                                        className="form-radio h-3 w-3 shrink-0 text-indigo-600 "
                                                        value="DELIVERY"
                                                    />
                                                    <span className='text-sm text-[#242424] dark:text-[#DBDBDB]'>Delivery</span>
                                                </label>

                                            </div>






                                        </div>
                                    </div>
                                </div>


                                <div className="flex flex-row justify-between gap-4 mt-2">
                                    <Button className={`h-[3.0625rem] font-medium text-xs sm:text-sm w-full bg-[#EFEFEF] text-black rounded-10 `} type='button' onClick={() => {
                                        setShowCart(false)
                                        setOpen(false)
                                    }}>Add more items</Button>
                                    <Button className={`h-[3.0625rem] font-medium text-xs sm:text-sm w-full bg-[#000] text-white rounded-10 flex justify-center items-center gap-3 `}>Proceed to checkout {isLoading && <SmallSpinner color='white' />}</Button>
                                </div>


                            </div>
                        </form>
                    </Dialog.Content>
                </Dialog.Portal>
            </Dialog.Root>

            <ErrorModal
                isErrorModalOpen={isErrorModalOpen}
                setErrorModalState={setErrorModalState}
                subheading={
                    errorModalMessage || 'Please check your inputs and try again.'
                }
            >
                <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                    <Button
                        className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
                        size="lg"
                        type="button"
                        onClick={closeErrorModal}
                    >
                        Okay
                    </Button>
                </div>
            </ErrorModal>


            {openTransferModal && <TransferCheckoutModal
                closeCheckoutModal={setOpen}
                open={openTransferModal}
                setOpen={setOpenTransferModal}
                setOrderId={setOrderId}
                storeId={storeId}
                transferData={transferData}

            />}

        </div>

    );
};

export default CheckoutDetails;
