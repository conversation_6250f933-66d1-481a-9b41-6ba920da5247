import * as React from "react";
import { SVGProps } from "react";

interface MinusIconProps extends SVGProps<SVGSVGElement> {
    size?: number;
    color?: string;
}

const MinusIcon: React.FC<MinusIconProps> = ({
    size = 16,
    color = "#929FA5",
    ...props
}) => (
    <svg
        fill="none"
        height={size}
        viewBox="0 0 16 16"
        width={size}
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path
            d="M2.5 8H13.5"
            stroke={color}
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
        />
    </svg>
);

export default MinusIcon;
