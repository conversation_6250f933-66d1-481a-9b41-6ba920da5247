import React from "react";
import NextIcon from "../icons/NextArrow";
import PrevArrow from "../icons/PrevArrow";

interface ArrowProps {
    className?: string;
    style?: React.CSSProperties;
    onClick?: React.MouseEventHandler<HTMLDivElement>;
}

const SampleNextArrow: React.FC<ArrowProps> = ({ className, style, onClick }) => {
    return (
        <div
            className={className}
            style={{ ...style, display: "block", position: "absolute", right: "2rem" }}
            onClick={onClick}
        >
            <NextIcon className="md:w-[56px] w-[40px]" />
        </div>
    );
};

const SamplePrevArrow: React.FC<ArrowProps> = ({ className, style, onClick }) => {
    return (
        <div
            className={className}
            style={{ ...style, display: "block", position: "absolute", left: "1rem" }}
            onClick={onClick}
        >
            <PrevArrow className="md:w-[56px] w-[40px]" />

        </div>
    );
};

export { SampleNextArrow, SamplePrevArrow };
