import Image from 'next/image'
import React from 'react'
interface Prop {
    imgsrc: string
}
const Banner = ({ imgsrc }: Prop) => {
    return (
        <div className="relative h-[9.6875rem] md:h-[23.75rem] w-full">
            {/* Light Blue Overlay */}
            <div className="absolute inset-0 bg-black/20 opacity-50 z-10" />

            <Image
                alt="banner image"
                className="object-cover"
                layout="fill"
                objectFit="cover"
                src={imgsrc}
            />
        </div>


    )
}

export default Banner