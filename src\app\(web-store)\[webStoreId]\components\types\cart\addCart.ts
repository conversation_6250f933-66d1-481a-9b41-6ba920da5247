export interface addToCartProps {
    id: string;
    product: Product;
    quantity: number;
}

export interface Product {
    id: string;
    category: string;
    all_branches: boolean;
    selected_branches: string[];
    created_at: string;
    updated_at: string;
    name: string;
    product_image_1: null;
    product_image_2: null;
    product_image_3: null;
    product_image_4: null;
    product_image_5: null;
    product_tag: null;
    product_description: null;
    sell_without_inventory: boolean;
    is_restaurant_item: boolean;
    product_price: string;
    selling_price: string;
    has_variants: boolean;
    discount: number;
    currency: string;
    company: string;
    created_by: string;
}