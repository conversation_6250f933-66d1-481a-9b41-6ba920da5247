import { Plane, SmallSpinner } from '@/icons/core';
import { <PERSON><PERSON>, <PERSON><PERSON>Header, Di<PERSON>, DialogContent, DialogBody, DialogFooter } from '.';
import React, { FC, ReactNode } from 'react';
import { cn } from '@/utils/classNames';

interface Props {
    isModalOpen: boolean;
    closeModal: () => void
    confirmFunction?: () => void
    isConfirmingAction?: boolean
    children: ReactNode
    title: string;
    icon?: ReactNode
}

const ConfirmActionModal: FC<Props> = ({ isModalOpen, confirmFunction, isConfirmingAction, closeModal, children, title, icon }) => {

    return (
        <Dialog modal={true} open={isModalOpen} >
            <DialogContent
                aria-label={title}
                className={cn(
                    'rounded-[10px]',
                    'my-6 bg-white'
                )}
                style={{
                    width: '92.5%',
                    minWidth: '300px',
                }}
                onPointerDownOutside={closeModal}
            >

                <DialogHeader className={cn('max-sm:sticky top-0 z-10')}>
                    <h5 className='text-base font-medium '>Confirm Action</h5>
                    <Button className='rounded-xl bg-white/20 px-5' size={'tiny'} variant={'unstyled'} onClick={closeModal}>
                        Close
                    </Button>
                </DialogHeader>

                <DialogBody className={cn('')}>
                    <div className='px-2'>
                        <span className='bg-main-solid rounded-full p-4 inline-block'>
                            {
                                icon ?
                                    icon
                                    :
                                    <Plane />
                            }
                        </span>

                        <h3 className='text-main-solid text-xl font-medium mt-1'>{title}</h3>
                        {children}
                    </div>
                </DialogBody>

                <DialogFooter className='flex items-center justify-end gap-4 bg-main-bg w-full rounded-2xl p-5'>
                    <Button className='py-2' variant="white" onClick={closeModal}>
                        Cancel
                    </Button>
                    <Button className='py-2' onClick={confirmFunction}>
                        Confirm
                        {
                            isConfirmingAction &&
                            <SmallSpinner />
                        }
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};

export default ConfirmActionModal;


