/** @type {import('tailwindcss').Config} */

// eslint-disable-next-line @typescript-eslint/no-var-requires
const plugin = require('tailwindcss/plugin');

module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
    '!./node_modules', // 👈
  ],
  theme: {
    extend: {
      backgroundPosition: {
        'bottom-right-offset': 'bottom -10% right -10%',
      },
      fontFamily: {
        sans: ['var(--font-sans)'],
        heading: ['var(--font-heading)'],
        'wix-display': ['var(--font-wix-display)'],
        clash: ['var(--font-clash)'],
        outfit: ['var(--font-outfit)'],
      },
      fontSize: {
        xxs: '.625rem',
      },
      colors: {
        'dash-dark-bg': 'var(--dash-dark-bg)',
        'light-dark-text': 'var(--light-dark-text)',
        'divider-line': 'var(--divider-line)',
        success: 'var(--success)',
        'light-bg': 'var(--light-bg)',
        'dash-light-bg': 'var(--dash-light-bg)',
        'light-accent-bg': 'var(--light-accent-bg)',
        'dark-text': 'var(--dark-text)',
        'light-text': 'var(--light-text)',
        'label-text': 'var(--label-text)',
        'solid-underline': 'var(--solid-underline)',
        'main-solid': 'var(--main-solid)',
        'main-solid-red': 'var(--main-solid-red)',
        'main-solid-light': 'var(--main-solid-light)',
        'main-bg': 'var(--main-bg)',
        'input-bg': 'var(--input-bg)',
        'input-placeholder': 'var(--input-placeholder)',
        'sidebar-link-active': 'var(--sidebar-link-active)',
        'card-border': 'var(--card-border)',
        'marketing-dark': 'var(--marketing-dark)',
        'marketing-light': 'var(--marketing-light)',
        'marketing-light-2': 'var(--marketing-light-2)',
        'marketing-light-text': 'var(--marketing-light-text)',
        'gold-gradient-bg': 'var(--test-gradient)',
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'var(--main-solid)',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        danger: {
          light: '#ffebeb',
          'light-hover': '#ffe0e0',
          'light-active': '#ffc0c0',
          DEFAULT: '#ff3333',
          'normal-hover': '#e62e2e',
          'normal-active': '#cc2929',
          dark: '#bf2626',
          'dark-hover': '#991f1f',
          'dark-active': '#731717',
          darker: '#591212',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      height: {
        'screen-small': [
          '100vh /* fallback for Opera, IE and etc. */',
          '100svh',
        ],
        'screen-small-without-header': [
          'calc(100vh - 4.875rem) /* fallback for Opera, IE and etc. */',
          'calc(100svh - 4.875rem)',
        ],
        'screen-small-without-header-desktop': [
          'calc(100vh - 8.4rem) /* fallback for Opera, IE and etc. */',
          'calc(100svh - 8.4rem)',
        ],
        'screen-large': [
          '100vh /* fallback for Opera, IE and etc. */',
          '100lvh',
        ],
        'modal-body': [
          'calc(75vh - 5rem) /* fallback for Opera, IE and etc. */',
          'calc(75svh - 5rem)',
        ],
      },
      minHeight: {
        'screen-small': [
          '100vh /* fallback for Opera, IE and etc. */',
          '100svh',
        ],
        'screen-small-without-header': [
          'calc(100vh - 4.875rem) /* fallback for Opera, IE and etc. */',
          'calc(100svh - 4.875rem)',
        ],
        'screen-small-without-header-desktop': [
          'calc(100vh - 8.4rem) /* fallback for Opera, IE and etc. */',
          'calc(100svh - 8.4rem)',
        ],
        'screen-large': [
          '100vh /* fallback for Opera, IE and etc. */',
          '100lvh',
        ],
      },
      maxHeight: {
        'modal-content': [
          'calc(75vh + 5rem) /* fallback for Opera, IE and etc. */',
          'calc(75svh + 5rem)',
        ],
        'modal-body': [
          'calc(75vh) /* fallback for Opera, IE and etc. */',
          'calc(75svh)',
        ],
      },
      boxShadow: {
        '3xl': '0 35px 60px -15px rgba(0, 0, 0, 0.3)',
        card: '0 4px 44px rgba(196,196,196,0.12)',
        popover: '0px 4px 44px 0px rgba(196, 196, 196, 0.24)',
      },
      borderRadius: {
        10: '0.625rem',
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic':
          'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
        'requisition-bg-gradient':
          'linear-gradient(269.03deg, #FFFFFF -34.39%, #F2F5FF 101.44%)',
        'requisition-companies-bg-gradient':
          'linear-gradient(84.66deg, #EBEFFB -9.29%, #FFFFFF 108.87%)',
        'requisition-single-company-bg-gradient':
          'linear-gradient(84.21deg, #EBEFFB 23.19%, #FFFFFF 135.47%)',
        'modal-bg-gradient':
          'linear-gradient(360deg, #FDFDFF 0%, #F2F5FF 100%)',
        'golden-gradient':
          'linear-gradient(116deg, #FFA63F -3.18%, #9A580D 47.93%)',
        'blue-gradient':
          'linear-gradient(280.11deg, #032282 6.2%, #077BDE 102.53%)',
        'green-gradient':
          'linear-gradient(279.36deg, #00623B 12.7%, #03FF1C 131.64%)',
        'ajo-savings-gradient':
          'linear-gradient(45deg, #414141 -16.26%, #000 51.75%)',
        'quick-savings-gradient':
          'linear-gradient(45deg, #F29C38 -16.26%, #9A580D 51.75%)',
        'halal-savings-gradient':
          'linear-gradient(45deg, #02CF25 -16.26%, #00623B 51.75%)',
        'checklock-savings-gradient':
          'linear-gradient(45deg, #0673D6 -16.26%, #032282 51.75%)',
        'blue-gradient':
          'linear-gradient(280.11deg, #032282 6.2%, #077BDE 102.53%)',
        'payroll-dashboard-gradient':
          'linear-gradient(23deg, #ebeffb 0%, #fff 100%)',
        'hris-dashboard-gradient':
          'linear-gradient(84deg, #EBEFFB -33.46%, #FFF 209.97%)',
        'payroll-show-gradient':
          'linear-gradient(18deg, #EBEFFB 0%, #FFF 100%)',
        'light-blue-gradient':
          'linear-gradient(141.22deg, #E0EEFF 49.61%, #107EE2 216.44%);',
        'dashed-blue-border':
          "url(\"data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='8' ry='8' stroke='%23032282FF' stroke-width='1.5' stroke-dasharray='10%2c 10' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e\")",
        'dashed-blue-border-2':
          "url(\"data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='8' ry='8' stroke='%23032282FF' stroke-width='1.5' stroke-dasharray='6%2c 9' stroke-dashoffset='5' stroke-linecap='square'/%3e%3c/svg%3e\")",

        'faded-white':
          'linear-gradient(90deg, #FFF 53.21%, rgba(255, 255, 255, 0.00) 103%)',
      },
      animation: {
        'vertical-slide': 'vertical-slide 20s infinite linear',
        'indeterminate-progress': 'indeterminate-progress 1.5s infinite linear',
      },
      keyframes: {
        'vertical-slide': {
          '0%': {
            transform: 'translate(0px, 0px)',
          },

          '100%': {
            transform: 'translate(0px, -100%)',
          },
        },
        'indeterminate-progress': {
          '0%': {
            transform: 'translateX(0) scaleX(0)',
          },
          '40%': {
            transform: 'translateX(0) scaleX(0.4)',
          },
          '100%': {
            transform: 'translateX(100%) scaleX(0.5)',
          },
        },
      },
    },
  },

  plugins: [
    require('@tailwindcss/container-queries'),
    require('tailwindcss-animate'),
    plugin(function ({ matchUtilities, theme }) {
      matchUtilities(
        {
          'bg-gradient': angle => ({
            'background-image': `linear-gradient(${angle}, var(--tw-gradient-stops))`,
          }),
        },
        {
          // values from config and defaults you wish to use most
          values: Object.assign(
            theme('bgGradientDeg', {}), // name of config key. Must be unique
            {
              10: '10deg', // bg-gradient-10
              15: '15deg',
              20: '20deg',
              25: '25deg',
              30: '30deg',
              45: '45deg',
              60: '60deg',
              90: '90deg',
              120: '120deg',
              135: '135deg',
              225: '225deg',
              358: '358deg',
            }
          ),
        }
      );
    }),
  ],
  darkMode: 'class',
};
