import React from 'react'
// import CartIcon from '../icons/CartIcon'
// import { addCommasToNumber } from '@/utils/numbers'

const ProductEmptyState = () => {
    // const products = [
    //     {
    //         image: "",
    //         produnct_name: "Product Name",
    //         selling_price: ""
    //     },
    //     {
    //         image: "",
    //         produnct_name: "Product Name",
    //         selling_price: ""
    //     },
    //     {
    //         image: "",
    //         produnct_name: "Product Name",
    //         selling_price: ""
    //     },
    // ]
    return (
        <div className='@container w-full border p-5 py-8'>
            <div className="flex flex-col gap-2 justify-center items-center">
                <h2 className='text-lg font-semibold'>Ouch! Nothing to display here.</h2>
                <p className='md:w-3/4 text-center text-sm text-[#37474F]'>You do not have any product </p>
            </div>

            {/* <div className={`mt-12  grid grid-cols-1 dark:bg-[#0D0D0D]  @md:grid-cols-2 @xl:grid-cols-2 @2xl:grid-cols-3 gap-[1.75rem] `}>

                {

                    products?.map((product, idx: number) => (
                        <div className="border-[.0219rem] border-[#898989] shrink-0 w-full rounded-[.5756rem] p-[1.0075rem] cursor-pointer" key={idx}>
                            <div className="w-full h-[6rem] md:h-[8.3181rem] bg-[#B2B5BE]">
                              
                            </div>
                            <div className="flex justify-between mt-[1rem]">
                                <div className="" >
                                  

                                    <h2 className='text-[#242424] font-semibold text-xs sm:text-sm dark:text-white line-clamp-1'>{product?.produnct_name}</h2>
                                    <p className='text-[#242424] font-semibold text-xs sm:text-sm  dark:text-white'>₦{addCommasToNumber(Number(product?.selling_price))}</p>
                                </div>
                                <div className="border-[.0144rem] shrink-0  border-[#B2B5BE] h-[1.5rem] md:h-[2.1587rem] w-[1.5rem] md:w-[2.1587rem] rounded-full p-[.25rem] md:p-[.4319rem] flex justify-center items-center cursor-pointer">
                                    <CartIcon className='w-[1.5rem] md:w-[1.25rem] ' />
                                </div>
                            </div>
                        </div>
                    ))
                }
            </div> */}
        </div>
    )
}

export default ProductEmptyState