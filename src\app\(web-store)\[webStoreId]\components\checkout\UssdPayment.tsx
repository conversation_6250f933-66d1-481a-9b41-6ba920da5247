"use client"
import React from 'react';
import * as Dialog from '@radix-ui/react-dialog';
import Image from 'next/image';
// import Countdown from '../utils/CountDown';
import { z } from 'zod';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/core';
import Link from 'next/link';
// import { DialogHeader } from '@/components/core';


interface Prop {
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    open: boolean;
}


const bankSchema = z.object({

    banks: z.string().min(1, { message: "choose" }),


});
export type contactInfoProps = z.infer<typeof bankSchema>;
const UssdPayment = ({ open, setOpen }: Prop) => {






    const {
        control,
        handleSubmit,
        formState: { errors },
    } = useForm<contactInfoProps>({
        resolver: zodResolver(bankSchema),
        defaultValues: {
            banks: ""

        },
        mode: "onChange",


    });
    const submit = (data: contactInfoProps) => {
        data
    }
    return (
        <div className='bg-yellow-900 w-full'>
            <Dialog.Root open={open}>
                <Dialog.Portal>
                    <Dialog.Overlay className="fixed   inset-0  bg-[#efefef]  transition-all duration-100 data-[state=closed]:animate-out data-[state=closed]:fade-out data-[state=open]:fade-in" />

                    <Dialog.Content className="w-[38.8125rem] z-[9999999] max-w-[98%] lg:max-w-[71.4375rem] fixed left-[50%] max-md:bottom-0  md:top-[50%] animate-in md:translate-y-[-50%] focus:outline-none data-[state=open]:fade-in-90 data-[state=open]:slide-in-from-bottom-10">

                        <div className="mb-5 flex items-start justify-start w-full   translate-x-[-50%]">
                            <button className='flex items-center font-bold text-base' onClick={() => setOpen(false)}><svg fill="none" height="20" viewBox="0 0 23 20" width="23" xmlns="http://www.w3.org/2000/svg">
                                <path d="M7.50039 17.225C7.34206 17.225 7.18372 17.1667 7.05872 17.0417L1.62539 11.6084C0.742057 10.725 0.742057 9.27502 1.62539 8.39168L7.05872 2.95835C7.30039 2.71668 7.70039 2.71668 7.94206 2.95835C8.18372 3.20002 8.18372 3.60002 7.94206 3.84168L2.50872 9.27502C2.10872 9.67502 2.10872 10.325 2.50872 10.725L7.94206 16.1583C8.18372 16.4 8.18372 16.8 7.94206 17.0417C7.81706 17.1584 7.65872 17.225 7.50039 17.225Z" fill="#292D32" />
                                <path d="M15.5004 17.225C15.3421 17.225 15.1837 17.1667 15.0587 17.0417L9.62539 11.6084C8.74206 10.725 8.74206 9.27502 9.62539 8.39168L15.0587 2.95835C15.3004 2.71668 15.7004 2.71668 15.9421 2.95835C16.1837 3.20002 16.1837 3.60002 15.9421 3.84168L10.5087 9.27502C10.1087 9.67502 10.1087 10.325 10.5087 10.725L15.9421 16.1583C16.1837 16.4 16.1837 16.8 15.9421 17.0417C15.8171 17.1584 15.6587 17.225 15.5004 17.225Z" fill="#292D32" />
                            </svg>

                                Back to checkout</button>

                        </div>



                        <div className="bg-white dark:bg-[#0D0D0D] max-sm: pt-4 shadow-[hsl(206_22%_7%_/_35%)_0px_10px_38px_-10px,_hsl(206_22%_7%_/_20%)_0px_10px_20px_-15px] md:mb-[3rem] sm:mb-auto  rounded-[1.125rem] py-10 px-6 sm:px-12  overflow-y-auto md:h-[79vh] translate-x-[-50%]  ">
                            <form onSubmit={handleSubmit(submit)}>
                                <div className="max-h-[60vh]  overflow-y-auto ">

                                    <div className="flex justify-between items-center">
                                        <div className="flex gap-[.325rem]   items-center cursor-pointer">
                                            <div className=" block h-[30px] w-[30px]">
                                                <Image alt='store-logo' height={30} src={`/images/web-store/storeLogo.png`} width={30} />
                                            </div>
                                            <div>
                                                <h2 className='font-semibold text-[8px] md:text-[.8194rem] text-black dark:text-[#E0E4E6]' style={{ wordBreak: "break-word" }}>Liberty Clothier & Stiches</h2>
                                                <p className='font-medium w-[80%] md:w-auto text-black text-[7px] md:text-[.4919rem] dark:text-[#E0E4E6]' style={{ wordBreak: "break-word" }}>Your trusted partner for all things fashion</p>
                                            </div>
                                        </div>
                                        <div className=""><p className=' text-xs md:text-base font-semibold dark:text-[#E0E4E6]'>09069003426</p></div>
                                    </div>
                                    <div className="bg-[#E6E9F24D] border border-[#E6E9F24D] rounded-[.75rem] px-10 py-4 mt-2 md:mt-5 w-full">
                                        <p className='text-sm md:text-lg font-medium dark:text-[#E0E4E6]'>Amount to pay</p>
                                        <h2 className='text-lg sm:text-[2.625rem] py-3 font-bold dark:text-[#E0E4E6]'>₦357.99 </h2>
                                        <div className="w-full border-t border-[#E4E7E9]">
                                            <div className="flex items-center gap-[1.125rem] mt-5">
                                                <p className='text-sm md:text-lg text-[#596072]'>Amount <span className='text-base font-bold text-[#191C1F] dark:text-[#E0E4E6]'>₦357.99</span></p>
                                                <p className='text-sm md:text-lg text-[#596072]'>Fee <span className='text-base font-bold text-[#191C1F] dark:text-[#E0E4E6]'>00.00</span></p>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="mt-4">
                                        <p className=' text-sm md:text-lg font-medium text-[#191C1F] dark:text-[#E0E4E6]'>Pay with USSD</p>
                                    </div>


                                    <div className="mt-6 bg-[#E6E9F24D] py-[1.5rem] max-md:mb-[5rem] w-full flex-col flex justify-center  px-[3.125rem] border-[#E6E9F24D] border-[.125rem] rounded-xl">
                                        <p className='text-sm md:text-lg font-medium'>Choose Bank</p>
                                        <div className={`flex flex-col mt-2${errors?.banks ? "border border-red-400" : ""}`}>
                                            {/* <label className='text-[#191C1F] text-sm ' htmlFor="state">Region/State</label> */}
                                            <Controller
                                                control={control}
                                                name="banks"
                                                render={({ field: { onChange, value, ref } }) => (
                                                    <Select value={value} onValueChange={onChange}>
                                                        <SelectTrigger
                                                            className="border bg-white"
                                                            id="state"
                                                            ref={ref}
                                                        >
                                                            <SelectValue placeholder="state" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            {[{ name: 'Access bank', value: 'Access bank' }, { name: 'First bank', value: 'First bank' }]?.map(
                                                                ({ name, value }) => {
                                                                    return (
                                                                        <SelectItem key={name} value={value}>
                                                                            {name}
                                                                        </SelectItem>
                                                                    );
                                                                }
                                                            )}
                                                        </SelectContent>
                                                    </Select>
                                                )}
                                            />
                                        </div>

                                        <div className="flex items-center justify-center gap-2 py-5">
                                            <h2 className='font-bold text-base md:text-[1.625rem]'>*737*2000*44353#</h2>
                                            <button className='py-[6px] px-4 rounded bg-white text-xs'>Copy</button>
                                        </div>

                                        <div className="py-2 text-center">
                                            <p className='text-xs text-[#646464]'>Copy or tap and dail the code to transfer</p>
                                        </div>
                                    </div>

                                </div>

                                <div className="fixed max-sm:bg-white dark:bg-[#0D0D0D] sm:relative max-sm:flex justify-center w-full  items-center border-none outline-none z-[999] h-[4rem] md:h-[6rem] max-sm:px-6 inset-x-0  max-md:bottom-10  mt-8">
                                    <button className={`px-2 bg-primary h-[3.5rem] w-full text-white rounded-lg font-semibold`}>I have made the transfer</button>
                                </div>
                                <p className='text-[.7rem] mt-2 text-center'>By making payment, you agree to Libertypay <Link href={"#"}>Terms of service and Privacy Policy</Link></p>
                            </form>
                        </div>
                    </Dialog.Content>
                </Dialog.Portal>
            </Dialog.Root>
        </div>
    );
};

export default UssdPayment;
