import { utils, read, WorkSheet } from 'xlsx';

export type ExpectedHeader = {
  name: string;
  type: 'text' | 'number';
};

// Process rows in chunks
const processRowsInChunks = (
  worksheet: WorkSheet,
  chunkSize: number,
  validateRow: (row: number) => void,
) => {
  const range = utils.decode_range(worksheet['!ref'] as string);
  const firstRow = range.s.r + 1; // Start from the second row, first row headers
  const lastRow = range.e.r;

  // Process rows in chunks
  for (let startRow = firstRow; startRow <= lastRow; startRow += chunkSize) {
    const endRow = Math.min(startRow + chunkSize - 1, lastRow);

    for (let row = startRow; row <= endRow; row++) {
      validateRow(row);
    }
  }
};

export default function validateExcel(file: File, expectedHeaders: ExpectedHeader[]): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (event) => {
      try {
        const data = new Uint8Array(event.target?.result as ArrayBuffer);
        const workbook = read(data, { type: 'array' });

        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];

        // Extract headers
        const headers: string[] = [];
        const range = utils.decode_range(worksheet['!ref'] as string);
        const firstRow = range.s.r;
        for (let C = range.s.c; C <= range.e.c; ++C) {
          const cell = worksheet[utils.encode_cell({ c: C, r: firstRow })];
          let hdr = `UNKNOWN ${C}`;
          if (cell && cell.t) hdr = utils.format_cell(cell);
          headers.push(hdr);
        }

        // Check for missing and extra headers
        const headerNames = expectedHeaders.map(header => header.name);
        const missingHeaders = headerNames.filter(header => !headers.includes(header));
        const extraColumns = headers.filter(header => !headerNames.includes(header));

        if (missingHeaders.length > 0) {
          return reject(`Missing headers: ${missingHeaders.join(', ')}`);
        } else if (extraColumns.length > 0) {
          return reject(`Columns not allowed: ${extraColumns.join(', ')}`);
        }

        // Create an object for expected header types
        const headerTypeObject: Record<string, 'text' | 'number'> = {};
        expectedHeaders.forEach(header => {
          headerTypeObject[header.name] = header.type;
        });

        const validateRow = (row: number) => {
          headerNames.forEach(header => {
            const type = headerTypeObject[header];
            const colIndex = headers.indexOf(header);
            if (colIndex === -1) return; // Skip if column not found

            const cell = worksheet[utils.encode_cell({ c: colIndex, r: row })];

            if (type === 'text') {
              if (cell && cell.t === 's' && cell.v.trim() === '') {
                throw new Error(`Empty cell found in "${header}" column at row ${row + 1}.`);
              }
              if (cell && cell.t && typeof cell.v !== 'string') {
                throw new Error(`Invalid data in "${header}" column at row ${row + 1}. Expected text.`);
              }
            } else if (type === 'number') {
              if (cell && cell.t && isNaN(Number(cell.v))) {
                throw new Error(`Invalid data in "${header}" column at row ${row + 1}. Expected a number.`);
              }
            }
          });
        };

        // Process rows in chunks of 100
        processRowsInChunks(worksheet, 100, (row) => {
          try {
            validateRow(row);
          } catch (error) {
            reject(`${error}`);
          }
        });

        resolve("Excel sheet is valid");
      } catch (error) {
        reject(`Error processing file: ${error}`);
      }
    };
    reader.readAsArrayBuffer(file);
  });
}
