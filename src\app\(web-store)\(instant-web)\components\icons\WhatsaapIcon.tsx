import * as React from "react";
import { SVGProps } from "react";
const WhatsappIcon = (props: SVGProps<SVGSVGElement>) => (
    <svg
        fill="none"
        height={24}
        viewBox="0 0 24 24"
        width={24}
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path
            d="M1.99916 22.7502C1.79916 22.7502 1.60913 22.6702 1.46913 22.5302C1.27913 22.3402 1.19918 22.0602 1.26918 21.8002L2.52919 17.0902C1.68919 15.5302 1.24916 13.7702 1.24916 11.9902C1.24916 6.06023 6.06916 1.24023 11.9992 1.24023C17.9292 1.24023 22.7492 6.06023 22.7492 11.9902C22.7492 17.9202 17.9292 22.7402 11.9992 22.7402C10.1892 22.7402 8.41919 22.2902 6.83919 21.4302L2.19917 22.7202C2.12917 22.7402 2.06916 22.7502 1.99916 22.7502ZM6.93916 19.8802C7.06916 19.8802 7.19917 19.9203 7.31917 19.9803C8.72917 20.8103 10.3492 21.2502 11.9992 21.2502C17.0992 21.2502 21.2492 17.1002 21.2492 12.0002C21.2492 6.90024 17.0992 2.75024 11.9992 2.75024C6.89916 2.75024 2.74916 6.90024 2.74916 12.0002C2.74916 13.6302 3.17915 15.2202 3.98915 16.6202C4.08915 16.7902 4.11917 17.0002 4.06917 17.1902L3.06917 20.9302L6.74916 19.9102C6.80916 19.8902 6.87916 19.8802 6.93916 19.8802Z"
            fill="white"
        />
        <path
            d="M14.7402 17.7603C14.1202 17.7603 13.4802 17.6203 12.8102 17.3303C12.1802 17.0603 11.5502 16.7003 10.9402 16.2503C10.3402 15.8103 9.75027 15.3103 9.21027 14.7703C8.67027 14.2203 8.17022 13.6403 7.73022 13.0403C7.28022 12.4103 6.92022 11.7903 6.66022 11.1803C6.38022 10.5203 6.24023 9.87029 6.24023 9.25029C6.24023 8.81029 6.32021 8.39029 6.47021 8.00029C6.63021 7.59029 6.89022 7.22028 7.23022 6.90028C7.87022 6.27028 8.79026 6.0403 9.52026 6.3903C9.77026 6.5003 9.98026 6.6803 10.1403 6.9203L11.3002 8.55027C11.4202 8.71027 11.5103 8.88027 11.5803 9.05027C11.6603 9.25027 11.7103 9.4503 11.7103 9.6403C11.7103 9.9003 11.6402 10.1603 11.5002 10.3903C11.4102 10.5403 11.2802 10.7203 11.1102 10.8903L10.9802 11.0303C11.0402 11.1103 11.1102 11.2103 11.2202 11.3303C11.4302 11.5703 11.6602 11.8303 11.9102 12.0803C12.1602 12.3203 12.4102 12.5603 12.6602 12.7703C12.7802 12.8703 12.8803 12.9503 12.9603 13.0003L13.1002 12.8603C13.2802 12.6803 13.4603 12.5403 13.6403 12.4503C13.9703 12.2403 14.4802 12.1903 14.9302 12.3803C15.0902 12.4403 15.2502 12.5303 15.4202 12.6503L17.0903 13.8303C17.3203 13.9903 17.5002 14.2103 17.6202 14.4603C17.7202 14.7103 17.7603 14.9303 17.7603 15.1603C17.7603 15.4603 17.6902 15.7503 17.5602 16.0303C17.4302 16.2903 17.2802 16.5203 17.1002 16.7303C16.7802 17.0803 16.4103 17.3403 16.0103 17.5103C15.6103 17.6803 15.1802 17.7603 14.7402 17.7603ZM8.79022 7.74028C8.73022 7.74028 8.53027 7.74028 8.28027 7.99028C8.09027 8.17028 7.96024 8.36029 7.87024 8.57029C7.78024 8.78029 7.74023 9.0203 7.74023 9.2603C7.74023 9.6803 7.84022 10.1303 8.04022 10.6103C8.25022 11.1103 8.56025 11.6403 8.94025 12.1703C9.33025 12.7003 9.77025 13.2303 10.2603 13.7203C10.7503 14.2003 11.2702 14.6503 11.8102 15.0503C12.3302 15.4303 12.8603 15.7303 13.3903 15.9603C14.1503 16.2903 14.8502 16.3703 15.4202 16.1303C15.6202 16.0503 15.8002 15.9103 15.9802 15.7303C16.0702 15.6303 16.1403 15.5303 16.2003 15.4003C16.2303 15.3303 16.2502 15.2503 16.2502 15.1803C16.2502 15.1603 16.2502 15.1303 16.2202 15.0703L14.5502 13.9103C14.4802 13.8603 14.4102 13.8203 14.3502 13.8003C14.3102 13.8203 14.2503 13.8503 14.1403 13.9603L13.7603 14.3403C13.4703 14.6303 13.0103 14.7103 12.6403 14.5803L12.4603 14.5003C12.2303 14.3803 11.9702 14.2003 11.6802 13.9503C11.4002 13.7103 11.1303 13.4603 10.8403 13.1803C10.5603 12.8903 10.3103 12.6203 10.0703 12.3403C9.81025 12.0303 9.63025 11.7803 9.51025 11.5703L9.40027 11.3103C9.37027 11.2103 9.36023 11.1003 9.36023 11.0003C9.36023 10.7203 9.46027 10.4703 9.65027 10.2703L10.0303 9.88029C10.1403 9.77029 10.1803 9.7103 10.2003 9.6703C10.1703 9.6003 10.1303 9.54029 10.0803 9.47029L8.91022 7.82029L8.79022 7.74028Z"
            fill="white"
        />
    </svg>
);
export default WhatsappIcon;
