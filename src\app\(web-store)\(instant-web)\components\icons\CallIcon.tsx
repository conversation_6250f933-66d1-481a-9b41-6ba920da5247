import * as React from "react";
import { SVGProps } from "react";
const CallIcon = (props: SVGProps<SVGSVGElement>) => (
    <svg
        fill="none"
        height={16}
        viewBox="0 0 16 16"
        width={16}
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path
            d="M11.633 15.1663C10.8797 15.1663 10.0863 14.9863 9.26634 14.6397C8.46634 14.2997 7.65968 13.833 6.87301 13.2663C6.09301 12.693 5.33967 12.053 4.62634 11.353C3.91967 10.6397 3.27967 9.88634 2.71301 9.11301C2.13967 8.31301 1.67967 7.51301 1.35301 6.73967C1.00634 5.91301 0.833008 5.11301 0.833008 4.35967C0.833008 3.83967 0.926341 3.34634 1.10634 2.88634C1.29301 2.41301 1.59301 1.97301 1.99967 1.59301C2.51301 1.08634 3.09967 0.833008 3.72634 0.833008C3.98634 0.833008 4.25301 0.893008 4.47967 0.999674C4.73967 1.11967 4.95967 1.29967 5.11967 1.53967L6.66634 3.71967C6.80634 3.91301 6.91301 4.09967 6.98634 4.28634C7.07301 4.48634 7.11967 4.68634 7.11967 4.87967C7.11967 5.13301 7.04634 5.37967 6.90634 5.61301C6.80634 5.79301 6.65301 5.98634 6.45967 6.17967L6.00634 6.65301C6.01301 6.67301 6.01967 6.68634 6.02634 6.69967C6.10634 6.83967 6.26634 7.07967 6.57301 7.43967C6.89967 7.81301 7.20634 8.15301 7.51301 8.46634C7.90634 8.85301 8.23301 9.15967 8.53967 9.41301C8.91967 9.73301 9.16634 9.89301 9.31301 9.96634L9.29967 9.99967L9.78634 9.51967C9.99301 9.31301 10.193 9.15967 10.3863 9.05967C10.753 8.83301 11.2197 8.79301 11.6863 8.98634C11.8597 9.05967 12.0463 9.15967 12.2463 9.29967L14.4597 10.873C14.7063 11.0397 14.8863 11.253 14.993 11.5063C15.093 11.7597 15.1397 11.993 15.1397 12.2263C15.1397 12.5463 15.0663 12.8663 14.9263 13.1663C14.7863 13.4663 14.613 13.7263 14.393 13.9663C14.013 14.3863 13.5997 14.6863 13.1197 14.8797C12.6597 15.0663 12.1597 15.1663 11.633 15.1663ZM3.72634 1.83301C3.35967 1.83301 3.01967 1.99301 2.69301 2.31301C2.38634 2.59967 2.17301 2.91301 2.03967 3.25301C1.89967 3.59967 1.83301 3.96634 1.83301 4.35967C1.83301 4.97967 1.97967 5.65301 2.27301 6.34634C2.57301 7.05301 2.99301 7.78634 3.52634 8.51967C4.05967 9.25301 4.66634 9.96634 5.33301 10.6397C5.99967 11.2997 6.71967 11.913 7.45967 12.453C8.17967 12.9797 8.91967 13.4063 9.65301 13.713C10.793 14.1997 11.8597 14.313 12.7397 13.9463C13.0797 13.8063 13.3797 13.593 13.653 13.2863C13.8063 13.1197 13.9263 12.9397 14.0263 12.7263C14.1063 12.5597 14.1463 12.3863 14.1463 12.213C14.1463 12.1063 14.1263 11.9997 14.073 11.8797C14.053 11.8397 14.013 11.7663 13.8863 11.6797L11.673 10.1063C11.5397 10.013 11.4197 9.94634 11.3063 9.89967C11.1597 9.83967 11.0997 9.77967 10.873 9.91967C10.7397 9.98634 10.6197 10.0863 10.4863 10.2197L9.97968 10.7197C9.71968 10.973 9.31968 11.033 9.01301 10.9197L8.83301 10.8397C8.55967 10.693 8.23967 10.4663 7.88634 10.1663C7.56634 9.89301 7.21967 9.57301 6.79967 9.15967C6.47301 8.82634 6.14634 8.47301 5.80634 8.07967C5.49301 7.71301 5.26634 7.39967 5.12634 7.13967L5.04634 6.93967C5.00634 6.78634 4.99301 6.69967 4.99301 6.60634C4.99301 6.36634 5.07967 6.15301 5.24634 5.98634L5.74634 5.46634C5.87967 5.33301 5.97967 5.20634 6.04634 5.09301C6.09967 5.00634 6.11967 4.93301 6.11967 4.86634C6.11967 4.81301 6.09967 4.73301 6.06634 4.65301C6.01967 4.54634 5.94634 4.42634 5.85301 4.29967L4.30634 2.11301C4.23967 2.01967 4.15967 1.95301 4.05967 1.90634C3.95301 1.85967 3.83967 1.83301 3.72634 1.83301ZM9.29967 10.0063L9.19301 10.4597L9.37301 9.99301C9.33967 9.98634 9.31301 9.99301 9.29967 10.0063Z"
            fill="white"
        />
    </svg>
);
export default CallIcon;
