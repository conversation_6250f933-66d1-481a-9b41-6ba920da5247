import React, { useState } from 'react'
import Banner from './Banner'
import CartIcon from '../icons/CartIcon'
import ProductDetails from './ProductDetails'
import { ProductTpes, StockProps } from '../types/products/producttype'
import { Spinner } from '@/icons/core'
import { FetchNextPageOptions, InfiniteData, InfiniteQueryObserverResult } from '@tanstack/react-query'
import useInfiniteScroll from 'react-infinite-scroll-hook'
import useCartStore from '../store/cartStore'
import { toast } from 'react-hot-toast'  // Import react-hot-toast
// import GeneralEmptyState from '@/app/(dashboard)/instant-web/misc/components/GeneralEmptyState'
import { addCommasToNumber } from '@/utils/numbers'
import ProductEmptyState from './ProductEmptyState'

interface PropsTypes {
    data: InfiniteData<ProductTpes> | undefined
    isLoading: boolean;
    fetchNextPage: (options?: FetchNextPageOptions | undefined) => Promise<InfiniteQueryObserverResult<ProductTpes, unknown>>
    hasNextPage: boolean | undefined;
    isFetchingNextPage: boolean;
    error: unknown,
    darkMode: string | boolean;
    header_images: string
    type: string;
    id: string;
    interface_theme: "LIGHT" | "DARK" | "SYSTEM";
    storeId: string

}

const ListStoreProduct = ({ data, isLoading, fetchNextPage, interface_theme, hasNextPage, isFetchingNextPage, storeId, error, darkMode, header_images, type, id }: PropsTypes) => {
    const [selectedProduct, setSelectedProduct] = useState<StockProps | undefined>()
    const [open, setOpen] = useState(false)
    const addToCart = useCartStore((state) => state?.addToCart)
    // const id = "0340ea7d-c5be-455a-8377-b0c28c523508";

    const addItemToCart = (product: StockProps) => {
        if (!product) return;
        const newCartItem = {
            company_id: id,
            id: product?.item,
            branch_id: product?.branch,
            product_img: product?.image,
            product_name: product?.item_name,
            product_description: "",
            price: Number(product.selling_price),
            quantity: 1,
            subTotal: Number(product.selling_price),
        };

        addToCart(storeId, [newCartItem]);
        toast.success(`${product.item_name} added to cart!`);  // Show success message
    };

    const nextPage = hasNextPage !== undefined;
    const [sentryRef] = useInfiniteScroll({
        loading: isFetchingNextPage || isLoading,
        hasNextPage: nextPage,
        onLoadMore: fetchNextPage,
        disabled: !!error,
        rootMargin: '0px 0px 400px 0px',
    });

    return (
        <div className={`overflow-y-auto @container ${interface_theme === "DARK" ? "dark" : ""}`}>
            {header_images && <div className="    ">
                <Banner bannerUrl={header_images} />
            </div>}
            <>
                {isLoading ? <div className='flex justify-center items-center w-full py-10'><Spinner color='blue' /></div> :
                    <div className={`mt-12  grid ${type === "customise" ? " grid-cols-1 dark:bg-[#0D0D0D]  @md:grid-cols-2 @xl:grid-cols-2 @2xl:grid-cols-3 gap-[1.75rem]" : " dark:bg-[#0D0D0D] px-4 sm:px-6 md:px-8 xl:px-10 2xl:px-[8.75rem] bg-white  mt-6  grid-cols-2  md:grid-cols-3  xl:grid-cols-4 2xl:grid-cols-5 gap-[1.75rem] "}`}>
                        {data?.pages?.length === 0 ? (
                            <div className=""><ProductEmptyState /></div>
                        ) : (
                            data?.pages.map((pageData, idx: number) => (
                                pageData?.results?.length === 0 ? (
                                    <div className='flex flex-col col-span-6 w-full   justify-center items-center flex-1 pb-16 ' key={idx}>
                                        <div className="w-full "><ProductEmptyState /></div>
                                        {/* <LinkButton className='flex  gap-x-2 w-max mx-auto' href={"/instant-web/campaign/new-campaign"}>
                                            <AddProductIcon />
                                            <p className="text-xs text-white">Add Campaign</p>
                                        </LinkButton> */}
                                    </div>
                                ) : (
                                    pageData.results.map((product) => (
                                        <div className="border-[.0219rem] border-[#898989] shrink-0 w-full rounded-[.5756rem] p-[1.0075rem] cursor-pointer" key={product?.item}>
                                            <div className="w-full h-[6rem] md:h-[8.3181rem] " onClick={() => {
                                                setOpen(true)
                                                setSelectedProduct(product)
                                            }}>
                                                {/* eslint-disable-next-line @next/next/no-img-element */}
                                                <img alt='product img' src={product?.image} style={{ width: "100%", height: "100%", objectFit: "contain" }} />
                                            </div>
                                            <div className="flex justify-between mt-[1rem]">
                                                <div className="" onClick={() => {
                                                    setOpen(true)
                                                    setSelectedProduct(product)
                                                }}>
                                                    {/* <p className='dark:text-white'>  </p> */}

                                                    <h2 className='text-[#242424] font-semibold text-xs sm:text-sm dark:text-white line-clamp-1'>{product?.item_name}</h2>
                                                    <p className='text-[#242424] font-semibold text-xs sm:text-sm  dark:text-white'>₦{addCommasToNumber(Number(product?.selling_price))}</p>
                                                </div>
                                                <div className="border-[.0144rem] shrink-0  border-[#B2B5BE] h-[1.5rem] md:h-[2.1587rem] w-[1.5rem] md:w-[2.1587rem] rounded-full p-[.25rem] md:p-[.4319rem] flex justify-center items-center cursor-pointer">
                                                    <CartIcon className='w-[1.5rem] md:w-[1.25rem] ' onClick={() => addItemToCart(product)} />
                                                </div>
                                            </div>
                                        </div>
                                    ))
                                )
                            ))
                        )}

                        {(isFetchingNextPage || hasNextPage) && (
                            <div
                                className=" flex w-full py-7 justify-center items-center"
                                ref={sentryRef}
                            >
                                {isFetchingNextPage && <Spinner color='blue' />}
                            </div>
                        )}
                    </div>}
                {open && <ProductDetails darkMode={darkMode} id={id} open={open} product={selectedProduct} setOpen={setOpen} storeId={storeId} />}
            </>

        </div>
    )
}

export default ListStoreProduct
