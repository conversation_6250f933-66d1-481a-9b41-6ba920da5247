"use client"
import React, { useState } from 'react';
import * as Dialog from '@radix-ui/react-dialog';
import {
    DialogHeader, ErrorModal, Popover, PopoverContent, PopoverTrigger, Button,
    Command,
    // CommandEmpty,
    // CommandInput,
    CommandItem,
    CommandList,
} from '@/components/core';
import { z } from 'zod';
import { Controller, useForm, useWatch } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
// import { CartData } from '../mock/cartData';
import TranserModal from './TranserModal';
import UssdPayment from './UssdPayment';
import useCartStore from '../store/cartStore';
import { contactInfo } from '../header-section/WebStoreHeader';
import { useCheckOutProduct } from '../api/checkout/checkout';
// import { CheckoutTypes } from '../types/checkout/checkoutTypes';
import { useRouter } from 'next/navigation';
import { Country, ICountry, IState, State } from 'country-state-city';
import toast from 'react-hot-toast';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { useErrorModalState } from '@/hooks';
import { AxiosError } from 'axios';
import { checkSuccessProp } from './types';
import DebounceInput from '../header-section/DebounceInput';
import PayOnDeliveryInvoiceModal from './PayonDeliveryInvoice';
// import { whatsappNotification } from '../types/checkout/whatsappNotification';


interface Prop {
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    open: boolean;
    contactInfoProps: contactInfo;
    storeId: string;
}

const contactSchema = z.object({
    buyer: z.object({
        first_name: z.string().min(1, { message: "Enter full name" }),
        last_name: z.string().min(1, { message: "Enter full name" }),
        middle_name: z.string(),
        address: z.string().min(1, { message: "Enter address" }),
        country: z.string().min(1, { message: "Enter country" }),
        state: z.string().min(1, { message: "Enter state" }),
        city: z.string().min(1, { message: "Enter city" }),
        email: z.string().email().min(1, { message: "Enter email" }),
        addition_information: z.string().optional(),
        phone_number: z.string({ required_error: "Enter phone_number." }).min(5, { message: "Enter phone_number" }),
        ship_to_different_address: z.boolean(),
        paymentOption: z.enum(['CARD', 'TRANSFER', 'DELIVERY', 'USSD', 'CASH']),
    })
});




export type contactInfoPropTypes = z.infer<typeof contactSchema>;

const BillingDetails = ({ open, setOpen, contactInfoProps, storeId }: Prop) => {
    const router = useRouter()
    const cartData = useCartStore((state) => state.carts[storeId]);
    const removeCart = useCartStore((state) => state?.removeCart)
    const [openTransferModal, setOpenTransferModal] = useState(false)
    const [countryList, setCountryList] = React.useState<ICountry[]>([]);
    const [stateList, setStateList] = React.useState<IState[]>([]);
    const [countryCode, setCountryCode] = React.useState('');
    const [searchCountry, setSearchCountry] = React.useState('');
    const [searchState, setSearchState] = React.useState('');
    const [openUssdModal, setOpenUssdModal] = useState(false)
    const [showDeliveryInvoice, setShowDeliveryInvoice] = useState(false)
    const [checkoutResponse, setCheckoutResponse] = useState<checkSuccessProp>()
    const [transferData, setTransferData] = useState<{
        amount: string,
        bank_name: string;
        bank_code: number;
        account_name: string;
        account_number: string;
        contact_phone_number: string;
        company_name: string;
        branchId: string;
        orderId: string;
        companyId: string;
    }>()
    const {
        control,
        register,
        handleSubmit,
        formState: { errors },
    } = useForm<contactInfoPropTypes>({
        resolver: zodResolver(contactSchema),
        defaultValues: {
            buyer: {
                first_name: contactInfoProps?.name,
                middle_name: "",
                last_name: "",
                country: "",
                address: "",
                state: "",
                city: "",
                email: contactInfoProps?.email,
                phone_number: contactInfoProps?.phone_number,
                ship_to_different_address: false,
                paymentOption: "CARD",
                addition_information: ""
            }

        },
        mode: "onChange",


    });
    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();

    // const [data, setData] = useState(CartData);
    const totalSum = cartData?.reduce((sum, item) => sum + item?.subTotal, 0);

    // Example values for shipping, discount, and tax
    const shipping = 0; // Free shipping
    const discount = 24; // Example discount value
    const tax = 61.99; // Example tax value

    // Calculate the grand total
    const grandTotal = totalSum + shipping - discount + tax;

    const { mutate: handleCheckout, isLoading } = useCheckOutProduct()
    const onSubmit = (data: contactInfoPropTypes) => {
        handleCheckout({
            buyer: data,
            cart: cartData,
            discount,
            shipping,
            tax,
            total_price: grandTotal

        }, {
            // onSuccess: (data: CheckoutTypes) => {
            onSuccess: (response: checkSuccessProp) => {
                if (data?.buyer?.paymentOption === "TRANSFER") {
                    setTransferData({
                        account_name: response?.payment_details?.account_name,
                        account_number: response?.payment_details?.account_number,
                        bank_code: response?.payment_details?.bank_code,
                        bank_name: response?.payment_details?.bank_name,
                        amount: String(response?.checkout_order?.total_price),
                        contact_phone_number: data?.buyer?.phone_number,
                        company_name: response?.checkout_order?.company,
                        branchId: response?.checkout_order?.branch_id,
                        companyId: response?.checkout_order?.company_id,
                        orderId: response?.checkout_order?.order_id

                    })
                    setOpenTransferModal(true)
                } else if (data?.buyer?.paymentOption === "CARD") {
                    // toast.success("success")
                    toast.success("success")
                    router.push(`${response?.payment_details?.payment_link}`)
                    setOpen(false)
                    removeCart(storeId)

                    // try {
                    //     whatsappNotification(response?.checkout_order?.order_id as string).then((data) => {
                    //         console.log(data?.message);

                    //     })
                    // } catch (error) {

                    // }
                    router.push(`${response?.payment_details?.payment_link}`)
                    setOpen(false)
                    removeCart(storeId)
                } else if (data?.buyer?.paymentOption === "DELIVERY") {
                    setCheckoutResponse(response)
                    toast.success("success")
                    setShowDeliveryInvoice(true)
                    // router?.push(`/instant-web/manage-website/select-company`)
                    // setOpen(false)
                    removeCart(storeId)
                }

            },
            onError: (error) => {
                const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                openErrorModalWithMessage(errorMessage);

            }
        })
        // }


    }



    React.useEffect(() => {
        setCountryList(Country.getAllCountries());
    }, []);

    const countryOptions = countryList
        ?.filter(country => country.name.toLowerCase().includes(searchCountry.toLowerCase()))
        ?.map(country => ({
            value: country.name,
            label: country.name,
            code: country.isoCode,
        }));

    const stateOptions = stateList
        ?.filter(state => state.name.toLowerCase().includes(searchState.toLowerCase()))
        ?.map(state => ({
            value: state.name,
            label: state.name,
            code: state.isoCode,
        }));

    const watchCountry = useWatch({ name: "buyer.country", control });

    React.useEffect(() => {
        const selectedCountry = countryOptions?.find(country => country.value === watchCountry);
        if (selectedCountry) {
            setCountryCode(selectedCountry.code);
        }
    }, [watchCountry, countryOptions]);

    React.useEffect(() => {
        setStateList(State.getStatesOfCountry(countryCode));
    }, [countryCode]);

    const [isCountryPopoverOpen, setIsCountryPopoverOpen] = useState<boolean>(false);
    const [isStatePopoverOpen, setIsStatePopoverOpen] = useState<boolean>(false);

    return (
        <div className='bg-yellow-900 w-full'>
            <Dialog.Root open={open}>
                <Dialog.Portal>
                    <Dialog.Overlay className="fixed !z-[999999999]  inset-0  bg-black/80 backdrop-blur-md transition-all duration-100 data-[state=closed]:animate-out data-[state=closed]:fade-out data-[state=open]:fade-in" />
                    <Dialog.Content className="max-w-[98%] !z-[999999999999999]  lg:max-w-[71.4375rem] fixed left-[50%] max-sm:bottom-0  overflow-y-auto sm:top-[50%] billing-modal max-h-[84vh]  md:h-[90vh] w-max translate-x-[-50%] dark:bg-[#0D0D0D] sm:translate-y-[-50%] rounded-lg bg-white shadow-[hsl(206_22%_7%_/_35%)_0px_10px_38px_-10px,_hsl(206_22%_7%_/_20%)_0px_10px_20px_-15px] animate-in focus:outline-none data-[state=open]:fade-in-90 data-[state=open]:slide-in-from-bottom-10">
                        <DialogHeader className="py-[1.35rem] px-4 lg:px-[3.2rem] bg-white z-[9999] sticky top-0 dark:bg-[#0D0D0D]">
                            <Dialog.Title className='text-[#242424] font-sans text-lg lg:text-[2rem] font-semibold dark:text-[#DBDBDB]'>Shopping cart</Dialog.Title>
                            <Dialog.Close className=" flex justify-center items-center"> <svg fill="none" height="32" viewBox="0 0 32 32" width="32" xmlns="http://www.w3.org/2000/svg" onClick={() => setOpen(false)}>
                                <path d="M14.1142 16.0001L3.72363 5.60949L5.60925 3.72388L15.9998 14.1143L26.3903 3.72388L28.2759 5.60949L17.8854 16.0001L28.2759 26.3905L26.3903 28.2762L15.9998 17.8857L5.60925 28.2762L3.72363 26.3905L14.1142 16.0001Z" fill="#596072" />
                            </svg></Dialog.Close>
                        </DialogHeader>
                        <form onSubmit={handleSubmit(onSubmit)}>
                            <div className="px-6 md:px-[3.5rem] relative h-[82vh] sm:h-auto overflow-y-auto grid grid-cols-1 sm:pb-[3.5rem] sm:grid-cols-[1.3fr_1fr] gap-5">
                                <div className="border-[.0625rem] p-4 md:p-6 border-[#E4E7E9] rounded  overflow-y-auto">
                                    <h2 className='py-[0.7rem] md:py-[1.25rem]  text-lg text-[#191C1F] dark:text-[#DBDBDB]'>Billing Information</h2>

                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-[.9819rem] w-full ">
                                        <div className={`flex flex-col mt-2`}>
                                            <label className='text-[#191C1F] text-sm   dark:text-[#DBDBDB]' htmlFor="first_name">First Name</label>
                                            <input className={`h-[2.75rem] outline-none  focus:outline focus:outline-blue-500  dark:bg-transparent dark:text-[#DBDBDB] px-3 border border-[#E4E7E9] rounded-[.125rem]  ${errors?.buyer?.first_name ? "border border-red-400" : ""}`} id='first_name' placeholder='' type="text" {...register("buyer.first_name")} />
                                        </div>
                                        <div className={`flex flex-col mt-2 `}>
                                            <label className='text-[#191C1F] text-sm    dark:bg-transparent dark:text-[#DBDBDB] ' htmlFor="middle_name">Middle Name</label>
                                            <input className={`h-[2.75rem] outline-none px-3  focus:outline focus:outline-blue-500  dark:bg-transparent dark:text-[#DBDBDB] border border-[#E4E7E9] rounded-[.125rem]  ${errors?.buyer?.middle_name ? "border border-red-400" : ""}`} id='middle_name' placeholder='' type="text" {...register("buyer.middle_name")} />
                                        </div>
                                        <div className={`flex flex-col mt-2 `}>
                                            <label className='text-[#191C1F] text-sm   dark:text-[#DBDBDB]' htmlFor="last_name">Last Name</label>
                                            <input className={`h-[2.75rem] outline-none dark:bg-transparent  focus:outline focus:outline-blue-500 dark:text-[#DBDBDB]  px-3 border border-[#E4E7E9] rounded-[.125rem]  ${errors?.buyer?.last_name ? "border border-red-400" : ""}`} id='last_name' placeholder='' type="text" {...register("buyer.last_name")} />
                                        </div>
                                    </div>
                                    <div className={`flex flex-col mt-4`}>
                                        <label className='text-[#191C1F] text-sm dark:text-[#DBDBDB]' htmlFor="address">Address</label>
                                        <input className={`h-[2.75rem] outline-none  focus:outline focus:outline-blue-500 dark:bg-transparent dark:text-[#DBDBDB]  px-3 border border-[#E4E7E9] rounded-[.125rem]  ${errors?.buyer?.address ? "border border-red-400" : ""}`} id='address' placeholder='' type="text" {...register("buyer.address")} />
                                    </div>




                                    <div className="grid mt-4 gap-[.9819rem] items-center justify-start grid-cols-1 lg:grid-cols-3">
                                        <div className="flex flex-col">
                                            <label className="text-sm font-medium" htmlFor="country">Country</label>
                                            <Controller
                                                control={control}
                                                name="buyer.country"
                                                render={({ field }) => (
                                                    <Popover open={isCountryPopoverOpen} onOpenChange={setIsCountryPopoverOpen}>
                                                        <PopoverTrigger asChild>
                                                            <Button className="w-full h-[2.75rem] border-[#E4E7E9] text-[#242424] dark:text-[#DBDBDB]  text-left" variant={"outlined"} onClick={() => setIsCountryPopoverOpen(true)}>
                                                                {field.value || 'Select Country'}
                                                            </Button>
                                                        </PopoverTrigger>
                                                        <PopoverContent align="start" className="w-full p-4 shadow-md bg-white z-[9999999999999999999999999999999]" side="bottom">
                                                            <Command>
                                                                {/* <Input
                                                                    placeholder="Search Country"
                                                                    value={searchCountry}
                                                                    onChange={(e) => setSearchCountry(e.target.value)}
                                                                /> */}


                                                                <DebounceInput
                                                                    className="bg-white rounded-md flex"
                                                                    // placeHolder='Search country'
                                                                    value={searchCountry ?? ""}
                                                                    onChange={(value) => setSearchCountry(String(value))}
                                                                />
                                                                <CommandList>
                                                                    {countryOptions?.map(country => (
                                                                        <CommandItem
                                                                            key={country.code}
                                                                            onSelect={() => {
                                                                                field.onChange(country.value);
                                                                                setSearchCountry('');
                                                                                setIsCountryPopoverOpen(false);
                                                                            }}
                                                                        >
                                                                            {country.label}
                                                                        </CommandItem>
                                                                    ))}
                                                                </CommandList>
                                                            </Command>
                                                        </PopoverContent>
                                                    </Popover>
                                                )}
                                            />
                                        </div>

                                        <div className="flex flex-col">
                                            <label className="text-sm font-medium" htmlFor="state">State</label>
                                            <Controller
                                                control={control}
                                                name="buyer.state"
                                                render={({ field }) => (
                                                    <Popover open={isStatePopoverOpen} onOpenChange={setIsStatePopoverOpen}>
                                                        <PopoverTrigger asChild>
                                                            <Button className="w-full text-left h-[2.75rem] border-[#E4E7E9] text-[#242424] dark:text-[#DBDBDB]  " disabled={!countryCode} variant={"outlined"} onClick={() => setIsStatePopoverOpen(true)}>
                                                                {field.value || 'Select State'}
                                                            </Button>
                                                        </PopoverTrigger>
                                                        <PopoverContent align="start" className="w-full p-4 shadow-md bg-white z-[9999999999999999999999]" side="bottom">
                                                            <Command>

                                                                <DebounceInput
                                                                    className="bg-white rounded-md flex"
                                                                    // placeHolder='Search State'
                                                                    value={searchState ?? ""}
                                                                    onChange={(value) => setSearchState(String(value))}
                                                                />
                                                                <CommandList>
                                                                    {stateOptions?.map(state => (
                                                                        <CommandItem
                                                                            key={state.code}
                                                                            onSelect={() => {
                                                                                field.onChange(state.value);
                                                                                setSearchState('');
                                                                                setIsStatePopoverOpen(false)
                                                                            }}
                                                                        >
                                                                            {state.label}
                                                                        </CommandItem>
                                                                    ))}
                                                                </CommandList>
                                                            </Command>
                                                        </PopoverContent>
                                                    </Popover>
                                                )}
                                            />
                                        </div>

                                        <div className="flex flex-col">
                                            <label className="text-sm font-medium" htmlFor="city">City</label>
                                            <input
                                                className={`h-[2.75rem] outline-none px-3 focus:outline focus:outline-blue-500 dark:bg-transparent dark:text-[#DBDBDB] border border-[#E4E7E9] rounded-[.125rem] ${errors?.buyer?.city ? "border border-red-400" : ""}`}
                                                id='city'
                                                placeholder='Enter your city'
                                                type="text"
                                                {...register("buyer.city")}
                                            />
                                        </div>
                                    </div>

                                    <div className="flex items-center w-full flex-col md:flex-row gap-[1.0731rem]">
                                        <div className={`flex flex-col w-full mt-4`}>
                                            <label className='text-[#191C1F] text-sm  dark:text-[#DBDBDB]' htmlFor="address">Email</label>
                                            <input className={`h-[2.75rem] w-full outline-none px-3  focus:outline focus:outline-blue-500 border border-[#E4E7E9] dark:bg-transparent dark:text-[#DBDBDB]  rounded-[.125rem]  ${errors?.buyer?.email ? "border border-red-400" : ""}`} id='address' placeholder='' type="text" {...register("buyer.email")} />
                                        </div>
                                        <div className={`flex flex-col w-full mt-4`}>
                                            <label className='text-[#191C1F] text-sm dark:text-[#DBDBDB] ' htmlFor="address">Phone number</label>
                                            <input className={`h-[2.75rem] w-full outline-none px-3  focus:outline focus:outline-blue-500 border border-[#E4E7E9] dark:bg-transparent dark:text-[#DBDBDB]  rounded-[.125rem]  ${errors?.buyer?.phone_number ? "border border-red-400" : ""}`} id='address' placeholder='' type="number" {...register("buyer.phone_number")} />
                                        </div>
                                    </div>

                                    {/* <div className="mt-4">
                                        <label className="flex items-center space-x-3">
                                            <input
                                                type="checkbox"
                                                {...register('buyer.ship_to_different_address')}
                                                className="form-checkbox h-4 w-4 text-[#032282]"
                                            />
                                            <span className="text-[#475156] text-sm dark:text-[#DBDBDB]">Ship into different address</span>
                                        </label>
                                        {errors?.buyer?.ship_to_different_address && <p className="text-red-600">{errors?.buyer?.ship_to_different_address.message}</p>}
                                    </div> */}

                                    <div className="mt-6">
                                        <p className="text-[#191C1F] text-sm dark:text-[#DBDBDB]">Payment Options:</p>
                                        <div className="flex items-center flex-wrap mt-2 gap-6">

                                            <label className="flex items-center space-x-3">
                                                <input
                                                    type="radio"
                                                    {...register('buyer.paymentOption')}
                                                    className="form-radio h-3 w-3 text-indigo-600"
                                                    value="CARD"
                                                />
                                                <span className='text-sm text-[#242424] dark:text-[#DBDBDB]'>Debit/Credit card</span>
                                            </label>
                                            <label className="flex items-center space-x-3">
                                                <input
                                                    type="radio"
                                                    {...register('buyer.paymentOption')}
                                                    className="form-radio h-3 w-3 text-indigo-600"
                                                    value="TRANSFER"
                                                />
                                                <span className='text-sm text-[#242424] dark:text-[#DBDBDB]'>Direct transfer</span>
                                            </label>
                                            <label className="flex items-center space-x-3">
                                                <input
                                                    type="radio"
                                                    {...register('buyer.paymentOption')}
                                                    className="form-radio h-3 w-3 text-indigo-600 "
                                                    value="DELIVERY"
                                                />
                                                <span className='text-sm text-[#242424] dark:text-[#DBDBDB]'>Pay on delivery</span>
                                            </label>
                                            {/* <label className="flex items-center space-x-3"> 
                                                <input
                                                    type="radio"
                                                    {...register('buyer.paymentOption')}
                                                    className="form-radio h-3 w-3 text-indigo-600"
                                                    value="USSD"
                                                />
                                                <span className='text-sm text-[#242424] dark:text-[#DBDBDB]'>Pay with ussd</span>
                                            </label> */}
                                        </div>
                                        {errors.buyer?.paymentOption && <p className="text-red-600">{errors.buyer?.paymentOption.message}</p>}
                                    </div>
                                    <div className={`flex flex-col w-full mt-4`}>
                                        <label className='text-[#191C1F] text-sm dark:text-[#DBDBDB]' htmlFor="address ">Additional Information</label>
                                        <span className='text-xs text-opacity-90 text-[#191C1F] dark:text-[#DBDBDB] py-2'>Order Notes (Optional)</span>
                                        <textarea className={`h-[4.75rem] py-2  focus:outline focus:outline-blue-500 w-full outline-none px-3 border border-[#E4E7E9] dark:text-[#DBDBDB] dark:bg-transparent rounded-[.125rem]  ${errors?.buyer?.addition_information ? "border border-red-400" : ""}`} id='address' placeholder='' {...register("buyer.addition_information")} />
                                    </div>
                                    {/* <input className={`h-[2.75rem] outline-none focus:outline focus:outline-blue-500 dark:bg-transparent dark:text-[#DBDBDB] px-3 border border-[#E4E7E9] rounded-[.125rem] ${errors?.buyer?.last_name ? "border border-red-400" : ""}`} id='last_name' placeholder='' type="text" {...register("buyer.last_name")} /> */}

                                </div>

                                <div className="relative">

                                    <div className='px-4  max-md:mb-[5rem] sm:px-6 border-[.0625rem] border-[#E4E7E9] rounded'>
                                        <h2 className='font-medium text-lg py-3 md:py-5 dark:text-[#DBDBDB]'>Order Summary</h2>
                                        <div className=" overflow-y-auto md:max-h-[52vh]">
                                            <div className="">
                                                {
                                                    cartData?.map((cart, idx: number) => (
                                                        <div className="flex items-center gap-3 mt-3" key={idx}>
                                                            <div className="w-[2.5rem] h-[2.5rem] shrink-0 ">
                                                                {/* eslint-disable-next-line @next/next/no-img-element */}
                                                                <img alt="" src={cart?.product_img} style={{ width: "100%", height: "100%" }} />
                                                            </div>
                                                            <div className=" flex-1">
                                                                <div className="w-32 sm:w-48 truncate  py-1">
                                                                    <p className='text-xs lg:text-base text-[#191C1F] truncate  dark:text-[#DBDBDB]'>{cart?.product_name} </p>
                                                                </div>
                                                                <p className='text-xs sm:text-sm flex items-center gap-2 dark:text-[#DBDBDB]'>{cart?.quantity} x <p className='font-bold'> {cart?.price}</p></p>
                                                            </div>
                                                        </div>
                                                    ))
                                                }
                                            </div>
                                            <div className="mt-4 space-y-2 md:space-y-6">
                                                <div className="flex justify-between items-center ">
                                                    <p className='text-sm text-[#5F6C72] dark:text-[#DBDBDB]'>Sub-total</p>
                                                    <p className='text-sm font-bold text-[#191C1F] dark:text-[#DBDBDB]'>₦{totalSum?.toFixed(2)}</p>
                                                </div>
                                                <div className="flex justify-between items-center ">
                                                    <p className='text-sm text-[#5F6C72] dark:text-[#DBDBDB]'>Shipping</p>
                                                    <p className='text-sm font-bold text-[#191C1F] dark:text-[#DBDBDB]'>Free</p>
                                                </div>
                                                <div className="flex justify-between items-center ">
                                                    <p className='text-sm text-[#5F6C72] dark:text-[#DBDBDB]'>Discount</p>
                                                    <p className='text-sm  font-boldtext-[#191C1F] dark:text-[#DBDBDB]'>₦{discount?.toFixed(2)}</p>
                                                </div>
                                                <div className="flex justify-between items-center ">
                                                    <p className='text-sm text-[#5F6C72] dark:text-[#DBDBDB]'>Tax</p>
                                                    <p className='text-sm font-bold text-[#191C1F] dark:text-[#DBDBDB]'>₦{tax?.toFixed(2)}</p>
                                                </div>
                                            </div>

                                            <div className="border-t border-[#E4E7E9] py-2 flex justify-between items-center mt-4 .">
                                                <h2>Total</h2>
                                                <p>₦{grandTotal?.toFixed(2)}</p>
                                            </div>

                                        </div>
                                        <div className="fixed max-sm:bg-white sm:relative max-sm:flex dark:bg-[#0e0909] justify-center items-center border-none outline-none  h-[6rem] max-sm:px-6 inset-x-0 bottom-0 mt-8">
                                            <button className={`px-2 bg-primary h-[3.5rem] w-full text-white rounded-lg font-semibold`}>{isLoading ? "Loading" : "Proceed to make Payment"}</button>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </form>

                    </Dialog.Content>
                </Dialog.Portal>
            </Dialog.Root>
            <ErrorModal
                isErrorModalOpen={isErrorModalOpen}
                setErrorModalState={setErrorModalState}
                subheading={
                    errorModalMessage || 'Please check your inputs and try again.'
                }
            >
                <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                    <Button
                        className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
                        size="lg"
                        type="button"
                        onClick={closeErrorModal}
                    >
                        Okay
                    </Button>
                </div>
            </ErrorModal>

            {showDeliveryInvoice && <PayOnDeliveryInvoiceModal checkoutResponse={checkoutResponse} open={showDeliveryInvoice} setOpen={setShowDeliveryInvoice} />}
            {openTransferModal && <TranserModal open={openTransferModal} setOpen={setOpenTransferModal} transferData={transferData} />}
            {openUssdModal && <UssdPayment open={openUssdModal} setOpen={setOpenUssdModal} />}
        </div>
    );
};

export default BillingDetails;

