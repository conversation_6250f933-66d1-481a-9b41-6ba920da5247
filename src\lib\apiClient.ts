import axios, { type AxiosInstance } from 'axios';

import { THROW_OUT_STATUS_CODES } from '@/utils/auth';

const payrollBaseURL = process.env.NEXT_PUBLIC_API_PAYROLL_BASE_URL;
const payrollInstantWageBaseURL = process.env.NEXT_PUBLIC_API_PAYROLL_BASE_URL;
const authBaseURL = process.env.NEXT_PUBLIC_API_AUTH_BASE_URL;
const spendManagementBaseURL =
  process.env.NEXT_PUBLIC_API_SPEND_MANAGEMENT_BASE_URL;
const requisitionsBaseUrl = process.env.NEXT_PUBLIC_API_REQUISITION_BASE_URL;
const stocksBaseURL = process.env.NEXT_PUBLIC_API_STOCKS_BASE_URL;
const savingsBaseUrl = process.env.NEXT_PUBLIC_API_SAVINGS_URL;
const billerBaseUrl = process.env.NEXT_PUBLIC_API_BILLER_BASE_URL;
const timeAttendanceBaseUrl = process.env.NEXT_PUBLIC_API_TIME_AND_ATTENDANCE_BASE_URL

const ALWAYS_AUTHORIZED_ENDPOINTS = ['user/login/create/'];

/**
 * Custom error class for representing errors in Fetch API requests.
 * Extends the base Error class and includes additional properties for status code and response data.
 */
export class CustomFetchError extends Error {
  status?: number;

  /**
   * Constructs a new instance of CustomFetchError.
   * @param {string} message - The error message.
   * @param {number|undefined} status - The HTTP status code.
   */
  constructor(message: string, status?: number) {
    super(message);
    this.name = 'CustomFetchError';
    this.status = status;
  }
}

/**
 * Makes an HTTP request using the Fetch API.
 * This should be used primarily for GET requests.
 * This would make it possible to benefit from the Next 13 cache system.
 *
 * @param endpoint - The API endpoint.
 * @param options - Optional configuration for the request (e.g., method, body, headers).
 * @returns A Promise that resolves to the response data or rejects with an error.
 * @template T - The expected response data type.
 * @template B - The type of the request body.
 *
 * @example
 * For GET
 * const response = await client<MyData>('endpoint');
 *
 * @example
 * For POST
 * const response = await client<MyResponse, MyRequestBody>('endpoint', {
 *   method: 'POST',
 *   body: { key: 'value' },
 * });
 */

export async function baseclient<T, B = void>(
  baseURL: string | undefined,
  endpoint: string,
  { body, ...customConfig }: Omit<RequestInit, 'body'> & { body?: B } = {}
): Promise<T | 'unauthorized'> {
  const headers = { 'Content-Type': 'application/json' };

  const config: RequestInit = {
    method: body ? 'POST' : 'GET',
    ...customConfig,
    headers: {
      ...headers,
      ...customConfig.headers,
    },
  };

  if (body) {
    config.body = JSON.stringify(body);
  }

  const response = await fetch(`${baseURL}/${endpoint}`, config);

  if (response.ok) {
    return (await response.json()) as T;
  } else {
    const statusCode = response.status;

    if (
      !!statusCode &&
      THROW_OUT_STATUS_CODES.includes(statusCode) &&
      !ALWAYS_AUTHORIZED_ENDPOINTS.includes(endpoint)
    ) {
      return 'unauthorized';
    }

    const errorMessage = await response.text();
    throw new CustomFetchError(errorMessage, statusCode);
  }
}

/**
 * Function to generate client functions with different baseURLs.
 * @param baseURL - The base URL for the client.
 * @returns A function that generates client functions with the specified baseURL.
 */
export function createClient(baseURL: string | undefined) {
  return function <T, B = void>(
    endpoint: string,
    options?: Omit<RequestInit, 'body'> & { body?: B }
  ): Promise<T | 'unauthorized'> {
    return baseclient<T, B>(baseURL, endpoint, options);
  };
}

/**
 * Makes an HTTP request using the Fetch API with additional handling for 403 status code.
 * @param baseURL - The API base URL.
 * @param endpoint - The API endpoint.
 * @param options - Optional configuration for the request (e.g., method, body, headers).
 * @returns A Promise that resolves to the response data, 'unauthorized', or 'forbidden'.
 * @template T - The expected response data type.
 * @template B - The type of the request body.
 *
 * @example
 * For GET:
 * const response = await clientWithForbidden<MyData>('endpoint');
 *
 * @example
 * For POST:
 * const response = await clientWithForbidden<MyResponse, MyRequestBody>('endpoint', {
 *   method: 'POST',
 *   body: { key: 'value' },
 * });
 */
export async function clientWithForbidden<T, B = void>(
  baseURL: string | undefined,
  endpoint: string,
  { body, ...customConfig }: Omit<RequestInit, 'body'> & { body?: B } = {}
): Promise<T | 'unauthorized' | 'forbidden'> {
  try {
    const response = await baseclient<T, B>(baseURL, endpoint, {
      body,
      ...customConfig,
    });

    if (response === 'unauthorized') {
      return 'unauthorized';
    } else {
      return response;
    }
  } catch (error) {
    if (error instanceof CustomFetchError && error.status === 403) {
      return 'forbidden';
    } else {
      throw error;
    }
  }
}

/**
 * Function to generate client functions with different baseURLs with 403 handling.
 * @param baseURL - The base URL for the client.
 * @returns A function that generates client functions with the specified baseURL.
 */
export function createClientWithForbidden(baseURL: string | undefined) {
  return function <T, B = void>(
    endpoint: string,
    options?: Omit<RequestInit, 'body'> & { body?: B }
  ): Promise<T | 'unauthorized' | 'forbidden'> {
    return clientWithForbidden<T, B>(baseURL, endpoint, options);
  };
}

export const authFetchClient = createClient(authBaseURL);
export const spendManagementFetchClient = createClient(spendManagementBaseURL);
export const payrollFetchClient = createClient(payrollBaseURL);
export const savingsFetchClient = createClient(savingsBaseUrl);
export const stocksFetchClient = createClient(stocksBaseURL);
export const requisitionsFetchClient = createClient(requisitionsBaseUrl);
export const billerFetchClient = createClient(billerBaseUrl);
export const timeAttendanceFetchClient = createClient(timeAttendanceBaseUrl);

// exports with forbidden
export const payrollFetchForbiddenClient = createClientWithForbidden(
  payrollInstantWageBaseURL
);

/**
 * @remarks
 * Axios has been added mainly for POST, PUT and other client requests.
 * Try the fetch wrapper for regular serverside GET queries.
 */
export const defaultAxiosClient = axios.create();

export const stocksAxiosClient = axios.create({
  baseURL: spendManagementBaseURL,
});

export const spendManagementAxiosClient = axios.create({
  baseURL: spendManagementBaseURL,
});
export const sendMoneyManagementAxiosClient = axios.create({
  baseURL: authBaseURL,
});
export const payrollAxiosClient = axios.create({
  baseURL: payrollBaseURL,
});

export const savingsAxiosClient = axios.create({
  baseURL: savingsBaseUrl,
});
export const requistionsAxiosClient = axios.create({
  baseURL: requisitionsBaseUrl,
});

export const tokenlessAuthAxiosClient = axios.create({
  baseURL: authBaseURL,
});

export const authAxiosClient = axios.create({
  baseURL: authBaseURL,
});

export const billerAxiosClient = axios.create({
  baseURL: billerBaseUrl,
});

export const makeStockRequestAxiosClient = axios.create({
  baseURL: billerBaseUrl,
});
export const getPickAddressRequestAxiosClient = axios.create({
  baseURL: authBaseURL,
});

export const timeAttendanceAxiosClient = axios.create({
  baseURL: timeAttendanceBaseUrl
})

export const setMultipleAxiosDefaultTokens = (token: string) => {
  defaultAxiosClient.defaults.headers.common.Authorization = `Bearer ${token}`;
  authAxiosClient.defaults.headers.common.Authorization = `Bearer ${token}`;
  spendManagementAxiosClient.defaults.headers.common.Authorization = `Bearer ${token}`;
  sendMoneyManagementAxiosClient.defaults.headers.common.Authorization = `Bearer ${token}`;
  savingsAxiosClient.defaults.headers.common.Authorization = `Bearer ${token}`;
  payrollAxiosClient.defaults.headers.common.Authorization = `Bearer ${token}`;
  stocksAxiosClient.defaults.headers.common.Authorization = `Bearer ${token}`;
  billerAxiosClient.defaults.headers.common.Authorization = `Bearer ${token}`;
  makeStockRequestAxiosClient.defaults.headers.common.Authorization = `Bearer ${token}`;
  makeStockRequestAxiosClient.defaults.headers.common.Authorization = `Bearer ${token}`;
  getPickAddressRequestAxiosClient.defaults.headers.common.Authorization = `Bearer ${token}`;
  timeAttendanceAxiosClient.defaults.headers.common.Authorization = `Bearer ${token}`
};

export const setAxiosDefaultToken = (token: string, client: AxiosInstance) => {
  client.defaults.headers.common.Authorization = `Bearer ${token}`;
};

export const deleteAxiosDefaultToken = () => {
  delete axios.defaults.headers.common.Authorization;
};
