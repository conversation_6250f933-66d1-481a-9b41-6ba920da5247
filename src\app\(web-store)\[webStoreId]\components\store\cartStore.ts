





// import { create } from 'zustand';
// import { devtools, persist } from 'zustand/middleware';

// export interface CartItem {
// id: string;
// company_id: string;
// branch_id: string;
// product_img: string;
// product_name: string;
// product_description: string;
// price: number;
// quantity: number;
// subTotal: number;
// }

// interface CartState {
//     carts: { [storeId: string]: CartItem[] };
//     addToCart: (storeId: string, cartDataArray: Omit<CartItem, 'subTotal'>[]) => void;
//     updateCart: (storeId: string, id: string, quantity: number) => void;
//     deleteFromCart: (storeId: string, id: string) => void;
//     removeCart: (storeId: string) => void;
// }

// const useCartStore = create<CartState>()(
//     devtools(
//         persist(
//             (set) => ({
//                 carts: {},
//                 addToCart: (storeId, cartDataArray) =>
//                     set((state) => {
//                         const updatedCart = [...(state.carts[storeId] || [])];
//                         cartDataArray.forEach((payload) => {
//                             const existingItem = updatedCart.find((cartItem) => cartItem.id === payload.id);
//                             if (existingItem) {
//                                 existingItem.quantity += payload.quantity;
//                                 existingItem.subTotal = existingItem.quantity * existingItem.price;
//                             } else {
//                                 updatedCart.push({
//                                     ...payload,
//                                     subTotal: payload.price * payload.quantity,
//                                 });
//                             }
//                         });
//                         return { carts: { ...state.carts, [storeId]: updatedCart } };
//                     }),
//                 updateCart: (storeId, id, quantity) =>
//                     set((state) => ({
//                         carts: {
//                             ...state.carts,
//                             [storeId]: state.carts[storeId]?.map((item) =>
//                                 item.id === id
//                                     ? { ...item, quantity, subTotal: item.price * quantity }
//                                     : item
//                             ) || [],
//                         },
//                     })),
//                 deleteFromCart: (storeId, id) =>
//                     set((state) => ({
//                         carts: {
//                             ...state.carts,
//                             [storeId]: state.carts[storeId]?.filter((item) => item.id !== id) || [],
//                         },
//                     })),
//                 removeCart: (storeId) =>
//                     set((state) => ({
//                         carts: { ...state.carts, [storeId]: [] },
//                     })),
//             }),
//             {
//                 name: 'cart-storage',
//             }
//         )
//     )
// );

// export default useCartStore;


import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

export interface CartItem {
    id: string;
    company_id: string;
    branch_id: string;
    product_img: string;
    product_name: string;
    product_description: string;
    price: number;
    quantity: number;
    subTotal: number;
    timestamp: number; // To track when the item was added or modified
}

interface CartState {
    carts: { [storeId: string]: CartItem[] };
    addToCart: (storeId: string, cartDataArray: Omit<CartItem, 'subTotal' | 'timestamp'>[]) => void;
    updateCart: (storeId: string, id: string, quantity: number) => void;
    deleteFromCart: (storeId: string, id: string) => void;
    removeCart: (storeId: string) => void;
}

// Utility function to sort cart items in descending order by timestamp
const sortCartItemsByTimestamp = (items: CartItem[]) => {
    return [...items].sort((a, b) => b.timestamp - a.timestamp);
};

const useCartStore = create<CartState>()(
    devtools(
        persist(
            (set) => ({
                carts: {},
                addToCart: (storeId, cartDataArray) =>
                    set((state) => {
                        const updatedCart = [...(state.carts[storeId] || [])];
                        cartDataArray.forEach((payload) => {
                            const existingItem = updatedCart.find((cartItem) => cartItem.id === payload.id);
                            const currentTimestamp = Date.now(); // Get current timestamp
                            if (existingItem) {
                                // Update quantity and subtotal, change the timestamp to the current time
                                existingItem.quantity += payload.quantity;
                                existingItem.subTotal = existingItem.quantity * existingItem.price;
                                existingItem.timestamp = currentTimestamp; // Update timestamp on modification
                            } else {
                                // Add new item with timestamp
                                updatedCart.push({
                                    ...payload,
                                    subTotal: payload.price * payload.quantity,
                                    timestamp: currentTimestamp, // Set timestamp when item is added
                                });
                            }
                        });
                        // Sort updated cart by timestamp in descending order
                        return { carts: { ...state.carts, [storeId]: sortCartItemsByTimestamp(updatedCart) } };
                    }),
                updateCart: (storeId, id, quantity) =>
                    set((state) => {
                        const updatedItems = state.carts[storeId]?.map((item) =>
                            item.id === id
                                ? {
                                    ...item,
                                    quantity,
                                    subTotal: item.price * quantity,
                                    timestamp: Date.now(), // Update timestamp on modification
                                }
                                : item
                        ) || [];
                        // Sort updated items by timestamp in descending order
                        return { carts: { ...state.carts, [storeId]: sortCartItemsByTimestamp(updatedItems) } };
                    }),
                deleteFromCart: (storeId, id) =>
                    set((state) => {
                        const updatedItems = state.carts[storeId]?.filter((item) => item.id !== id) || [];
                        // Sort updated items by timestamp in descending order
                        return { carts: { ...state.carts, [storeId]: sortCartItemsByTimestamp(updatedItems) } };
                    }),
                removeCart: (storeId) =>
                    set((state) => ({
                        carts: { ...state.carts, [storeId]: [] },
                    })),
            }),
            {
                name: 'cart-storage',
            }
        )
    )
);

export default useCartStore;
