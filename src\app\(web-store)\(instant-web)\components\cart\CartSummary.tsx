"use client"
import useCartStore, { CartItem } from '@/app/(web-store)/[webStoreId]/components/store/cartStore';
import React, { useState } from 'react'
import CloseIcon from '../icons/CloseIcon';
import FullScreenIcon from '../icons/FullScreenIcon';
import { Button } from '@/components/core';
import Image from 'next/image';
import { addCommasToNumber } from '@/utils/numbers';
import CheckoutDetails from '../checkout/CheckoutDetails';
// import RemoveIcon from '../icons/RemoveIcon';
import MinusIcon from '@/app/(web-store)/[webStoreId]/components/icons/MinusIcon';
import AddIcon from '@/app/(web-store)/[webStoreId]/components/icons/AddIcon';
// import RemoveIcon from '../icons/RemoveIcon';
interface Prop {
    storeId: string;
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    setShowCartFullScreen: React.Dispatch<React.SetStateAction<boolean>>
    data: CartItem[];
    setShowSuccessModal: React.Dispatch<React.SetStateAction<boolean>>
    setOrderId: React.Dispatch<React.SetStateAction<string>>
}
const CartSummary = ({ storeId, setOpen, setShowCartFullScreen, setShowSuccessModal, setOrderId }: Prop) => {
    const carts = useCartStore((state) => state.carts[storeId]);
    const removecart = useCartStore((state) => state.removeCart);
    const [data, setData] = useState(carts);
    const totalQuantity = carts?.reduce((sum, item) => sum + item.quantity, 0);
    const deleteFromCart = useCartStore((state) => state?.deleteFromCart)
    const updateCart = useCartStore((state) => state?.updateCart)
    const cart = useCartStore((state) => state.carts[storeId]);
    const [showCheckoutModal, setShowCheckoutModal] = useState(false)

    const handleQuantityChange = (cartId: string, rowIndex: number, increment: number) => {
        const currentCartItem = cart?.find(item => item?.id === cartId);
        if (!currentCartItem) return;

        const newQuantity = Math.max(0, currentCartItem?.quantity + increment); // Ensure quantity doesn't drop below 0

        if (newQuantity <= 0) {

            deleteFromCart(storeId, cartId); // Remove item from cart if quantity reaches 0
            setData(data?.filter((prod => String(prod?.id) !== String(cartId))))
        } else {
            updateCart(storeId, cartId, newQuantity);
        }


        setData((prevData) =>
            prevData?.map((item, index) =>
                index === rowIndex
                    ? {
                        ...item,
                        quantity: Math.max(0, item?.quantity + increment), // Prevents quantity from going below 1
                        subTotal: item.price * Math.max(1, item?.quantity + increment),
                    }
                    : item
            )
        );

    };

    const totalSum = cart.reduce((sum, item) => sum + item.subTotal, 0);

    // Example values for shipping, discount, and tax
    const shipping = 0; // Free shipping
    const discount = 0; // Example discount value
    const tax = 0; // Example tax value

    // Calculate the grand total
    const grandTotal = totalSum + shipping - discount + tax;
    return (
        <div className='p-4'>
            <div className="flex justify-between items-center">
                <div className="flex items-center gap-3">
                    <p className='font-medium text-lg text-black'>Cart </p>
                    <div className=" w-[2rem] h-[2rem] rounded-full bg-[#EFEFEF] flex  justify-center items-center">
                        <p className='text-black text-sm font-semibold'>{totalQuantity}</p>
                    </div>
                </div>
                <div className="flex items-center gap-3">
                    <div className="" onClick={(() => cart?.length > 0 && setShowCheckoutModal(true))}>
                        <Button className='bg-black h-[2.375rem] rounded-[.625rem]' disabled={cart?.length === 0}>Checkout</Button>

                    </div>

                    <div onClick={() => removecart(storeId)}>
                        <Button className=' h-[2.375rem] rounded-[.625rem]' disabled={cart?.length === 0} variant={"outlined"}>Clear</Button>
                    </div>

                    <div className="flex justify-center h-8 w-8 bg-[#EFEFEF] rounded-full items-center" onClick={() => {
                        if (data?.length > 0) {
                            setShowCartFullScreen(true)
                            setOpen(false)
                        }
                    }}>
                        <Button className='bg-transparent' disabled={cart?.length === 0}> <FullScreenIcon /></Button>
                    </div>
                    <div className="flex justify-center h-8 w-8 bg-[#EFEFEF] rounded-full items-center" onClick={() => setOpen(false)}>
                        <Button className='bg-transparent'>  <CloseIcon height={10} width={10} /></Button>
                    </div>
                </div>
            </div>

            <div className="mt-10">
                {
                    // Sort cart items in ascending order by price, then get the last six items
                    cart
                        ?.slice(0, 5) // Get the last six items after sorting
                        ?.map((cartItem, idx: number) => (
                            <div className="flex items-start gap-7 my-4 justify-between" key={idx}>

                                <div className="flex items-start gap-3">
                                    <div className='relative bg-[#f4f3f1] w-[60px] h-[47px] p-2 rounded-10 flex justify-center items-center'>
                                        {cartItem?.product_img !== null ? (
                                            <Image
                                                alt={``}
                                                className="rounded-lg"
                                                height={44}
                                                src={cartItem?.product_img}
                                                width={52}
                                            />
                                        ) : (
                                            <div className="w-[3.25rem] h-[2.75rem] rounded-lg"></div>
                                        )}
                                    </div>
                                    <div className="">
                                        <p className='text-sm pb-1 '>{cartItem?.product_name}</p>
                                        <div className='flex  mt-1  rounded items-center w-full  gap-x-[.2813rem]'>
                                            <Button className='p-1 bg-[#F9F9F9] w-[2.625rem] h-[1.3rem] md:h-[1.7rem] rounded-lg flex justify-center items-center' onClick={() => handleQuantityChange(String(cartItem?.id), idx, -1)}>
                                                <MinusIcon className='w-[10px] md:w-4' fill='white' size={14} />
                                            </Button>
                                            <input
                                                className='max-w-[2.6875rem] border-[#E8E6E6] font-bold border-[.0187rem]  bg-transparent outline-none h-[1.7rem] rounded-[.3125rem] text-center text-sm text-black dark:text-white'
                                                type='number'
                                                value={cart?.find(cart => cart.id === cartItem?.id)?.quantity}
                                                readOnly
                                            />
                                            <Button className='p-1 bg-[#F9F9F9] w-[2rem] h-[1.3rem] md:h-[1.7rem] rounded-lg flex justify-center items-center' onClick={() => handleQuantityChange(String(String(cartItem?.id)), idx, 1)}>
                                                <AddIcon className='w-[10px] md:w-4' fill='white' size={14} />
                                            </Button>
                                        </div>
                                    </div>
                                </div>


                                <div className="">
                                    <p className='text-black text-base font-semibold font-sans'>&#8358;{addCommasToNumber(Number(cartItem?.subTotal))}</p>
                                </div>


                            </div>
                        ))
                }
                <div className="flex items-center justify-between p-6">
                    <p className='text-[#080808] text-lg'>Items total:</p>
                    <h2 className='text-black text-xl font-bold font-outfit'>&#8358;{Number(grandTotal).toLocaleString()}</h2>
                </div>
            </div>
            {
                showCheckoutModal && <CheckoutDetails
                    CheckoutDetails={{ first_nmae: "", last_name: "", phone_number: "" }}
                    open={showCheckoutModal}
                    setOpen={setShowCheckoutModal}
                    setOrderId={setOrderId}
                    setShowCart={setOpen}
                    setShowSuccessModal={setShowSuccessModal}
                    storeId={storeId}

                />
            }
        </div>
    )
}

export default CartSummary