import {
  compareAsc,
  differenceInHours,
  differenceInMinutes,
  format,
  parse,
} from 'date-fns';

export function checkMeetingStatus(scheduledTime: string): {
  text: string;
  late: boolean;
} {
  // 11:10:00
  if (!scheduledTime) {
    return {
      late: false,
      text: '',
    };
  }
  const currentTime = new Date();

  const scheduledTimeDate = parse(scheduledTime, 'HH:mm:ss', new Date());

  if (compareAsc(currentTime, scheduledTimeDate) === 1) {
    const minutesLate = differenceInMinutes(currentTime, scheduledTimeDate);
    const hoursLate = differenceInHours(currentTime, scheduledTimeDate);
    const remainingMinutes = minutesLate % 60;

    if (hoursLate > 0) {
      return {
        late: true,
        text: `You're ${hoursLate} hour(s) and ${remainingMinutes} minute(s) late!`,
      };
    } else {
      return {
        late: true,
        text: `You're ${minutesLate} minute(s) late!`,
      };
    }
  } else {
    return {
      late: false,
      text: `You have a meeting by ${format(
        scheduledTimeDate,
        'h:mm a'
      )}`,
    };
  }
}
