/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { CSVLink } from "react-csv";

interface DownloadCSVProps {
  data: any;
  headers: any;
  filename: any;
  label?: string;
}

export function DownloadCSV({ data = '', headers, filename, label = 'Export' }: DownloadCSVProps) {
  return (
    <CSVLink data={data} filename={filename} headers={headers}>
      {label}
    </CSVLink >
  );
}
