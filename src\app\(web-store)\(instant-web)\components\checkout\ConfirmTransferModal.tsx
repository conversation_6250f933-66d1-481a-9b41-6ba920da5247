import React, { useEffect } from 'react';
import * as Dialog from '@radix-ui/react-dialog';

// import { StockProps } from '@/app/(web-store)/[webStoreId]/components/types/products/producttype'.........;
import CloseIcon from '../icons/CloseIcon';
import { DialogHeader } from '@/components/core';
import { QueryObserverResult, RefetchOptions, RefetchQueryFilters } from '@tanstack/react-query';
import { transferTypesProp } from '@/app/(web-store)/[webStoreId]/components/api/checkout/verifyTransfer';


interface Prop {
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    open: boolean;
    refetch: <TPageData>(options?: (RefetchOptions & RefetchQueryFilters<TPageData>) | undefined) => Promise<QueryObserverResult<transferTypesProp, unknown>>

}

const ConfirmTransferModal = ({ open, setOpen, refetch }: Prop) => {
    useEffect(() => {
        const intervalId = setInterval(() => {
            refetch(); // Call refetch every 10 seconds
        }, 10000); // 10000ms = 10 seconds

        // Cleanup interval on component unmount
        return () => clearInterval(intervalId);
    }, [refetch]);
    return (
        <div className={`w-full z-[99999999999999999999999999] `}>
            <Dialog.Root open={open}>
                <Dialog.Portal>
                    <Dialog.Overlay className="fixed inset-0  z-[999999999999999]  bg-black/80 backdrop-blur-0 transition-all duration-100 data-[state=closed]:animate-out data-[state=closed]:fade-out data-[state=open]:fade-in" />
                    <Dialog.Content className={` w-[100%] overflow-x-hidden sm:max-w-[32.625rem] z-[999999999999] fixed left-[50%] max-md:bottom-0 md:top-[50%]  h-[25.6875rem] overflow-y-auto  translate-x-[-50%] md:translate-y-[-50%] rounded-[1.25rem] bg-white dark:bg-[#0D0D0D]  shadow-[hsl(206_22%_7%_/_35%)_0px_10px_38px_-10px,_hsl(206_22%_7%_/_20%)_0px_10px_20px_-15px] animate-in focus:outline-none`}>
                        <DialogHeader className="py-[1.25rem] px-4 md:px-[1.5rem] bg-white dark:bg-[#0D0D0D]  z-[9999] sticky top-0">
                            <Dialog.Title className='text-[#242424] text-xs md:text-base font-semibold dark:text-white'></Dialog.Title>
                            <Dialog.Close className="flex justify-center h-[2.625rem] w-[2.625rem] bg-[#EFEFEF] rounded-full items-center" onClick={() => setOpen(false)}>
                                <CloseIcon height={15} width={15} />
                            </Dialog.Close>
                        </DialogHeader>
                        <div className="flex flex-col justify-center  items-center w-full overflow-x-hidden">
                            <div className="">
                                <svg className='animate-spin' fill="none" height="92" viewBox="0 0 92 92" width="92" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="46" cy="46" r="45.5" stroke="black" strokeDasharray="6 6" />
                                    <path d="M67.8415 33.0349C67.631 32.273 67.1373 31.639 66.4531 31.2506L49.0537 21.3842C48.3695 20.9958 47.5726 20.898 46.8108 21.1085C46.0514 21.319 45.4174 21.8103 45.029 22.4918C45.029 22.4943 45.0265 22.4943 45.0265 22.4968C44.3523 23.6872 44.5954 25.1507 45.5302 26.0655L41.8463 32.5637C40.6509 34.6713 40.3702 36.8115 40.9842 39.1069L42.2422 43.8057L37.5634 45.1389C35.2779 45.7905 33.5863 47.1312 32.3909 49.2388L28.707 55.737C28.2083 55.6067 27.6795 55.6067 27.1683 55.7496C26.4065 55.9601 25.7724 56.4538 25.384 57.1379C24.5796 58.5538 25.0808 60.3607 26.4967 61.1651L43.8962 71.0315C44.3548 71.2921 44.8535 71.4149 45.3472 71.4149C46.3772 71.4149 47.3771 70.8761 47.9209 69.9188L47.9234 69.9163C48.5975 68.7259 48.3519 67.2624 47.4172 66.3502L51.1011 59.852C52.2965 57.7444 52.5771 55.6042 51.9632 53.3088L50.7052 48.61L55.384 47.2768C57.6695 46.6252 59.3611 45.2845 60.5565 43.1769L64.2404 36.6787C64.4885 36.7438 64.7391 36.7764 64.9872 36.7764C66.0172 36.7764 67.0171 36.2376 67.5609 35.2803C67.5634 35.2778 67.5634 35.2753 67.5634 35.2753C67.9543 34.5886 68.052 33.7942 67.8415 33.0349ZM46.6127 69.172C46.2192 69.8662 45.3346 70.1092 44.6429 69.7158L27.2435 59.8494C26.9102 59.659 26.6696 59.3507 26.5643 58.9748C26.4616 58.6014 26.5092 58.2105 26.6971 57.8772C26.8876 57.5439 27.1958 57.3033 27.5718 57.1981C27.7021 57.163 27.8324 57.1454 27.9627 57.1454C28.2083 57.1454 28.4514 57.2081 28.6694 57.3334L46.0689 67.1998C46.7605 67.5957 47.0036 68.4804 46.6127 69.172ZM59.2454 42.4297C58.2405 44.199 56.8847 45.2791 54.9725 45.8229L49.5796 47.3591C49.1836 47.4719 48.9505 47.8804 49.0583 48.2788L50.5068 53.6944C51.0205 55.614 50.7925 57.3332 49.7901 59.1025L46.1538 65.5154L44.4371 64.5431L30.0651 56.3935L33.7014 49.9805C34.7063 48.2113 36.0621 47.1312 37.9743 46.5873L43.3672 45.0511C43.7632 44.9384 43.9963 44.5299 43.8885 44.1314L42.44 38.7158C41.9263 36.7962 42.1543 35.077 43.1568 33.3078L46.7931 26.8948L62.8818 36.017L59.2454 42.4297ZM66.255 34.5329C66.255 34.5354 66.2524 34.5354 66.2524 34.5354C65.859 35.227 64.9744 35.4701 64.2827 35.0767L46.8833 25.2103C46.1916 24.8169 45.946 23.9372 46.3369 23.2431C46.3369 23.2405 46.3394 23.2405 46.3394 23.238C46.5299 22.9047 46.8381 22.6642 47.2141 22.5589C47.5875 22.4561 47.9784 22.5038 48.3117 22.6917L65.7111 32.5581C66.0444 32.7486 66.285 33.0568 66.3903 33.4327C66.4905 33.8086 66.4429 34.1996 66.255 34.5329Z" fill="black" />
                                </svg>

                            </div>
                            <div className="mt-4  overflow-x-hidden">

                                <div className="flex flex-col justify-center  items-center w-full overflow-x-hidden">
                                    <h2 className='text-lg md:text-[1.5rem] font-semibold font-outfit'>Payment Confirmation in Progress</h2>
                                    <p className='w-[80%] text-center text-sm md:text-base text-[#818181] py-1'>Please do not close this page while we confirm  your payment.</p>
                                </div>

                            </div>

                        </div>

                    </Dialog.Content>
                </Dialog.Portal>
            </Dialog.Root>



        </div>

    );
};


export default ConfirmTransferModal