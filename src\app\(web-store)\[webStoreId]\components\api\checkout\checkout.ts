import axios from "axios";
import { contactInfoPropTypes } from "../../checkout/BillingDetails";
import { CartItem } from "../../store/cartStore";
import { useMutation } from "@tanstack/react-query";


interface Prop {


    buyer: contactInfoPropTypes;

    cart: CartItem[];
    shipping: number,
    discount: number,
    tax: number,
    total_price: number,
}
export const checkOutProduct = async ({ buyer: { buyer }, cart, discount, shipping, tax, total_price }: Prop) => {
    const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_STOCKS_BASE_URL}orders/create_order_checkout/`, {
        company_id: cart[0]?.company_id,
        branch_id: cart[0]?.branch_id,
        buyer,
        contact: {
            name: buyer?.first_name,
            phone_number: buyer?.phone_number
        },
        cart,
        discount,
        shipping,
        total_price,
        tax

    }
    );
    return response?.data;
};

export const useCheckOutProduct = () => {
    return useMutation({
        mutationFn: checkOutProduct
    })
}
