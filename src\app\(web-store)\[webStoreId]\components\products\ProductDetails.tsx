import React, { useState, useEffect } from 'react';
import * as Dialog from '@radix-ui/react-dialog';
import { <PERSON><PERSON>, DialogHeader } from '@/components/core';
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { CartData } from '../mock/cartData';
import MinusIcon from '../icons/MinusIcon';
import AddIcon from '../icons/AddIcon';
import { StockProps } from '../types/products/producttype';
import { useQuery } from '@tanstack/react-query';
import { fetchProdcutsDetails } from '../api/products/fetchProductDetails';
import { SampleNextArrow, SamplePrevArrow } from './CustomArrow';
import Slider from 'react-slick';
import { Spinner } from '@/icons/core';
import { Item } from '../types/products/productDetailsTypes';
import useCartStore, { CartItem } from '../store/cartStore';
import { fetchProdcuts } from '../api/products/fetchProducts';
import { CaretLeftIcon, CaretRightIcon } from '@radix-ui/react-icons';
import BillingDetails from '../checkout/BillingDetails';

interface Prop {
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    open: boolean;
    product: StockProps | undefined;
    id: string;
    darkMode: string | boolean;
    storeId: string
}

const ProductDetails = ({ open, setOpen, product, id, darkMode, storeId }: Prop) => {
    const productId = String(product?.item);
    const [openBillingModal, setOpenBillingModal] = useState(false);

    const [productDetailId, setProductDetailId] = useState(productId)
    const addToCart = useCartStore(state => state.addToCart);
    const updateCart = useCartStore(state => state.updateCart);
    const deleteFromCart = useCartStore(state => state.deleteFromCart);
    const cart = useCartStore((state) => state.carts[storeId]);
    const [cartDataArray, setCartDataArray] = useState<CartItem[]>(CartData);

    useEffect(() => {
        setCartDataArray(cart);
    }, [cart]);

    const isInCart = cartDataArray?.some(cart => cart.id === productId);

    const addItemToCart = (productItem: Item | undefined) => {
        if (!productItem) return;

        const newCartItem = {
            id: productId,
            branch_id: String(product?.branch),
            company_id: id,
            product_img: productItem?.images[0],
            product_name: productItem?.name,
            product_description: String(productItem?.product_description),
            price: Number(productItem.selling_price),
            quantity: 1,
            subTotal: Number(productItem.selling_price),
        };

        addToCart(storeId, [newCartItem]);
    };



    const handleQuantityChange = (productId: string, change: number) => {
        const currentCartItem = cart?.find(item => item?.id === productId);
        if (!currentCartItem) return;

        const newQuantity = Math.max(0, currentCartItem?.quantity + change); // Ensure quantity doesn't drop below 0

        if (newQuantity <= 0) {
            deleteFromCart(storeId, productId); // Remove item from cart if quantity reaches 0
        } else {
            updateCart(storeId, productId, newQuantity);
        }
    };

    const { data, isLoading } = useQuery({
        queryFn: () => fetchProdcutsDetails(id, productDetailId),
        queryKey: ["product-details", id, productDetailId],
    });

    const settings = {
        dots: true,
        infinite: true,
        speed: 500,
        slidesToShow: 1,
        slidesToScroll: 1,
        nextArrow: <SampleNextArrow className='bg-blue-900 w-[40px]' />,
        prevArrow: <SamplePrevArrow />,
        customPaging: function (i: number) {
            return (
                <>
                    {data?.data?.items[0]?.images[i] !== null && <a className='relative w-full flex items-center justify-between'>
                        {/* eslint-disable-next-line @next/next/no-img-element */}
                        <img alt='img' className='w-12 h-12' src={`${data?.data?.items[0]?.images[i]}`} />
                        {/* <div className="dot w-2 h-2 md:w-4 md:h-4 bg-gray-300 rounded-full"></div> */}
                    </a>}
                </>
            );
        },
        appendDots: (dots: string) => (
            <div className="dots">
                <ul className="m-0">{dots}</ul>
            </div>
        ),
        dotsClass: "slick-dots slick-thumb",
    };

    const { data: productData } = useQuery({
        queryFn: () => fetchProdcuts(id, "", "", 1, 3000),
        queryKey: ["fetch-products-id", id, productDetailId]
    })


    const itemIds: string[] = productData?.results?.map((item) => item?.item) || [];

    interface AdjacentIds {
        before: string | null;
        after: string | null;
        error?: string;
    }

    function getAdjacentIds(productId: string, idsArray: string[]): AdjacentIds {
        const index = idsArray.indexOf(productId);

        if (index === -1) {
            return { before: null, after: null, error: "Product ID not found" };
        }

        const before = index > 0 ? idsArray[index - 1] : null;
        const after = index < idsArray.length - 1 ? idsArray[index + 1] : null;

        return { before, after };
    }

    // Example usage:
    const result = getAdjacentIds(productDetailId, itemIds);

    const images = data?.data?.items[0]?.images?.filter(image => image !== null) as string[];

    return (
        <div className={`w-full z-[9999] `}>
            <Dialog.Root open={open}>
                <Dialog.Portal>
                    <Dialog.Overlay className={`fixed inset-0 bg-black/50 backdrop-blur-0 transition-all duration-100 `} />
                    <Dialog.Content className={`${darkMode ? "dark" : ""} w-[100%] lg:max-w-[55.4375rem] z-[99999] fixed left-[50%] max-md:bottom-0 md:top-[50%] max-h-[95vh] h-[98vh] overflow-y-auto md:h-[90vh] translate-x-[-50%] md:translate-y-[-50%] rounded-lg bg-white dark:bg-[#0D0D0D]  shadow-[hsl(206_22%_7%_/_35%)_0px_10px_38px_-10px,_hsl(206_22%_7%_/_20%)_0px_10px_20px_-15px] animate-in focus:outline-none`}>
                        <DialogHeader className="py-[2.25rem] px-4 md:px-[3.5rem] bg-white dark:bg-[#0D0D0D] z-[9999] sticky top-0">
                            <Dialog.Title className='text-[#242424] text-lg md:text-[2rem] font-semibold dark:text-white'>Product details</Dialog.Title>
                            <Dialog.Close className="flex justify-center items-center">
                                <svg fill="none" height="26" viewBox="0 0 32 32" width="26" xmlns="http://www.w3.org/2000/svg" onClick={() => setOpen(false)}>
                                    <path d="M14.1142 16.0001L3.72363 5.60949L5.60925 3.72388L15.9998 14.1143L26.3903 3.72388L28.2759 5.60949L17.8854 16.0001L28.2759 26.3905L26.3903 28.2762L15.9998 17.8857L5.60925 28.2762L3.72363 26.3905L14.1142 16.0001Z" fill="#596072" />
                                </svg>
                            </Dialog.Close>
                        </DialogHeader>
                        {isLoading ? (
                            <div className='flex justify-center w-full items-center py-10'><Spinner color='blue' /></div>
                        ) : (
                            <div className={`px-4 md:px-[3.5rem] pb-[3.5rem] bg-white dark:bg-[#0D0D0D]  `}>
                                <div className="slider-container">
                                    <Slider {...settings}>
                                        {images?.map((img, index) => (
                                            <div className='w-full h-[16rem] md:h-[25.25rem] relative' key={index}>
                                                <div className='w-full h-[16rem] md:h-[27.25rem] relative dark:bg-[#0D0D0D] '>
                                                    <Image
                                                        alt={product?.image || 'Product image'}
                                                        src={img}
                                                        fill
                                                        style={{ objectFit: "contain" }}
                                                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                                                    />
                                                </div>
                                            </div>
                                        ))}
                                    </Slider>
                                </div>

                                <div className="mt-[4rem] md:mt-[7.375rem]">
                                    <div className="flex justify-between flex-wrap items-center">
                                        <h2 className='font-semibold text-base md:text-[1.625rem] dark:text-white'>{data?.data?.items[0]?.name}</h2>
                                        <p className='font-bold text-[0.8rem] md:text-[1rem] dark:text-white'>₦{data?.data?.items[0]?.selling_price}</p>
                                    </div>
                                    <p className='text-sm md:text-lg text-[#242424] font-medium mt-2 dark:text-white'>{data?.data?.items[0]?.product_description}</p>
                                </div>
                                <div className="flex justify-between mt-[2rem] flex-col gap-5 md:flex-row md:items-center w-full">
                                    <div className=" flex items-center gap-6">
                                        <Button className='p-3 md:px-[2.25rem]  md:py-[0.8rem] bg-[#032282] text-xs md:text-base font-bold dark:bg-white dark:text-black text-white rounded-[.5625rem]'
                                            onClick={() => {
                                                !isInCart && addItemToCart(data?.data?.items[0])
                                                setOpenBillingModal(true)
                                            }}>Buy Now</Button>
                                        {!isInCart ? (
                                            <Button
                                                className='sm:px-[1.6875rem] p-3 md:py-[0.8rem] border-[#032282] border-[0.6px] text-xs md:text-base font-bold text-[#032282] rounded-[.5625rem]'
                                                variant={"outlined"}
                                                onClick={() => addItemToCart(data?.data?.items[0])}
                                            >
                                                Add to cart
                                            </Button>
                                        ) : (
                                            <div className='flex border border-[#E4E7E9] rounded items-center p-2 md:px-4 md:py-2 gap-x-[.2813rem]'>
                                                <button className='p-1 bg-[#F9F9F9] w-[2.625rem] h-[1.8rem] md:h-[2.1875rem] rounded-lg flex justify-center items-center' onClick={() => handleQuantityChange(String(productId), -1)}>
                                                    <MinusIcon className='w-[10px] md:w-4' />
                                                </button>
                                                <input
                                                    className='max-w-[2rem] md:max-w-[3.5rem] bg-transparent outline-none border-none text-center text-base text-black dark:text-white'
                                                    type='number'
                                                    value={cartDataArray.find(cart => cart.id === productId)?.quantity}
                                                    readOnly
                                                />
                                                <button className='p-1 bg-[#F9F9F9] w-[2.625rem] h-[1.8rem] md:h-[2.1875rem] rounded-lg flex justify-center items-center' onClick={() => handleQuantityChange(String(productId), 1)}>
                                                    <AddIcon className='w-[10px] md:w-4' />
                                                </button>
                                            </div>
                                        )}
                                    </div>
                                    <div className="flex gap-4 md:gap-6 ">
                                        <button className='flex items-center text-xs border-[0.52px] border-[#6670854D] p-3 rounded-md text-[#667085] txet-sm dark:text-white' disabled={!result?.before} onClick={() => setProductDetailId(String(result?.before))}><CaretLeftIcon /> Prev Product </button>
                                        <button className='flex items-center text-xs border-[0.52px] border-[#6670854D] p-3 rounded-md text-[#667085] txet-sm dark:text-white' disabled={!result?.after} onClick={() => setProductDetailId(String(result?.after))}> Next Product <CaretRightIcon /></button>
                                    </div>
                                </div>
                            </div>
                        )}
                    </Dialog.Content>
                </Dialog.Portal>
            </Dialog.Root>

            {openBillingModal && <BillingDetails contactInfoProps={{
                email: "",
                name: "",
                phone_number: "",

            }} open={openBillingModal}
                setOpen={setOpenBillingModal}
                storeId={storeId} />}

        </div>

    );
};

export default ProductDetails;
