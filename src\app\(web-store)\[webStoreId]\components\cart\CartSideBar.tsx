"use client"
import { zodResolver } from '@hookform/resolvers/zod';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { contactInfo } from '../header-section/WebStoreHeader';
import { addCommasToNumber } from '@/utils/numbers';

interface Prop {
    data: {
        id: string;
        product_img: string;
        product_name: string;
        product_description: string;
        price: number;
        quantity: number;
        subTotal: number;
    }[];
    setCartModal: React.Dispatch<React.SetStateAction<boolean>>;
    setOpenBilling: React.Dispatch<React.SetStateAction<boolean>>;
    setContactInfoProps: React.Dispatch<React.SetStateAction<contactInfo>>
}




const contactSchema = z.object({
    contact: z.object({
        name: z.string().min(1, { message: "Enter full name" }),
        email: z.string().email().min(1, { message: "Enter email" }),
        phone_number: z.string({ required_error: "Enter phone_number." }).min(10, { message: "Enter valid phone_number" }),
    })
});
export type contactInfoProps = z.infer<typeof contactSchema>;
const CartSideBar = ({ data, setCartModal, setOpenBilling, setContactInfoProps }: Prop) => {
    // const [showCheckout, setShowCheckout] = useState(false)
    // Calculate the total sum of subtotals
    const totalSum = data.reduce((sum, item) => sum + item.subTotal, 0);

    // Example values for shipping, discount, and tax
    const shipping = 0; // Free shipping
    const discount = 24; // Example discount value
    const tax = 61.99; // Example tax value

    // Calculate the grand total
    const grandTotal = totalSum + shipping - discount + tax;




    const {
        register,
        handleSubmit,
        formState: { errors },
    } = useForm<contactInfoProps>({
        resolver: zodResolver(contactSchema),
        defaultValues: {
            contact: {
                name: "",
                email: "",
                phone_number: ""
            }

        },
        mode: "onChange",


    });

    const onSubmit = (data: contactInfoProps) => {
        setContactInfoProps({
            email: data?.contact?.email,
            phone_number: data?.contact?.phone_number,
            name: data?.contact?.name
        })
        setOpenBilling(true)
        setCartModal(false)
    }
    return (
        <div className="relative">
            <div className='px-6 py-4 border-[.0625rem] border-[#E4E7E9] rounded'>
                <h2 className='font-medium text-lg'>Cart Summary</h2>
                <div className="mt-4 space-y-4">
                    <div className="flex justify-between items-center ">
                        <p className='text-sm text-[#5F6C72] dark:text-[#E0E4E6]'>Sub-total</p>
                        <p className='text-sm text-[#191C1F] dark:text-[#E0E4E6]'>₦{addCommasToNumber(Number(totalSum?.toFixed(2)))}</p>
                    </div>
                    <div className="flex justify-between items-center ">
                        <p className='text-sm text-[#5F6C72] dark:text-[#E0E4E6]'>Shipping</p>
                        <p className='text-sm text-[#191C1F] dark:text-[#E0E4E6]'>Free</p>
                    </div>
                    <div className="flex justify-between items-center ">
                        <p className='text-sm text-[#5F6C72] dark:text-[#E0E4E6]'>Discount</p>
                        <p className='text-sm text-[#191C1F] dark:text-[#E0E4E6]'>₦{discount?.toFixed(2)}</p>
                    </div>
                    <div className="flex justify-between items-center ">
                        <p className='text-sm text-[#5F6C72] dark:text-[#E0E4E6]'>Tax</p>
                        <p className='text-sm text-[#191C1F] dark:text-[#E0E4E6]'>₦{tax?.toFixed(2)}</p>
                    </div>
                </div>

                <div className="border-t border-[#E4E7E9] py-2 flex justify-between items-center mt-4">
                    <h2 className='dark:text-[#E0E4E6]'>Total</h2>
                    <p className='dark:text-white'>₦{addCommasToNumber(Number(grandTotal?.toFixed(2)))}</p>
                </div>
            </div>
            <div className='px-6 py-4 border-[.0625rem] mt-5 border-[#E4E7E9] rounded'>
                <h2 className='font-medium text-lg'>Contact Information </h2>
                <form onSubmit={handleSubmit(onSubmit)} >
                    <div className={`flex flex-col mt-2`}>
                        <label className='text-[#191C1F] text-sm dark:text-[#E0E4E6]' htmlFor="first_name">First Name</label>
                        <input className={`h-[2.75rem] outline-none px-3 border border-[#E4E7E9] dark:bg-transparent dark:text-[#E0E4E6]  rounded-[.125rem]  ${errors?.contact?.name ? "border border-red-400" : ""}`} id='first_name' placeholder='Full name' type="text" {...register("contact.name")} />
                    </div>
                    <div className={`flex flex-col mt-2`}>
                        <label className='text-[#191C1F] text-sm dark:text-[#E0E4E6]' htmlFor="first_name">Email</label>
                        <input className={`h-[2.75rem] outline-none px-3 border border-[#E4E7E9] dark:bg-transparent dark:text-[#E0E4E6] rounded-[.125rem]  ${errors?.contact?.email ? "border border-red-400" : ""}`} id='first_name' placeholder='email' type="text" {...register("contact.email")} />
                    </div>

                    <div className={`flex flex-col  mt-4 mb-[4rem] md:mb-0`}>
                        <label className='text-[#191C1F] text-sm dark:text-[#E0E4E6]' htmlFor="phone">Phone Number</label>
                        <input className={`h-[2.75rem] outline-none px-3 border  text-sm border-[#E4E7E9] dark:text-[#E0E4E6] dark:bg-transparent rounded-[.125rem] ${errors?.contact?.phone_number ? "border border-red-400" : ""}`} id='phone' placeholder='phone number' type="number"  {...register("contact.phone_number")} />
                    </div>


                    <div className="fixed lg:relative border-none outline-none z-[999] bg-white dark:bg-[#0D0D0D] h-[9rem] md:h-[6rem] flex justify-center items-center md:block max-lg:px-6 inset-x-0 bottom-0 md:mt-8">
                        <button className={`px-8 bg-primary h-[3.5rem] w-full text-white rounded-lg font-semibold`}>Proceed To Checkout</button>
                    </div>
                </form>
            </div>

        </div>
    );
};

export default CartSideBar;
