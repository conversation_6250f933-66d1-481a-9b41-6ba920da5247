"use client"
import React, { useEffect, useState } from 'react';
import * as Dialog from '@radix-ui/react-dialog';
import Image from 'next/image';
import Countdown from '../utils/CountDown';
import { useQuery } from '@tanstack/react-query';
import { verifyTransfer } from '../api/checkout/verifyTransfer';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { AxiosError } from 'axios';
import { useErrorModalState } from '@/hooks';
import { Button, ErrorModal } from '@/components/core';
import { useRouter } from 'next/navigation';
import { whatsappNotification } from '../api/checkout/whatsappNotification';
import { SmallSpinner } from '@/icons/core';
import TranserConfirmModal from './TransferConfirmationModal';
// import { DialogHeader } from '@/components/core';


interface Prop {
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    open: boolean;
    transferData: {
        amount: string;
        bank_name: string;
        bank_code: number;
        account_name: string;
        account_number: string;
        contact_phone_number: string;
        company_name: string;
        branchId: string;
        orderId: string;
        companyId: string;
    } | undefined
}
const TranserModal = ({ open, setOpen, transferData }: Prop) => {
    const router = useRouter()
    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();
    const [showConfirmModal, setShowConfirmModal] = useState(false)
    const [transferMsg, setTransferMsg] = useState("")
    const { refetch, isFetching } = useQuery({
        queryFn: () => verifyTransfer(transferData?.companyId as string, String(transferData?.branchId), String(transferData?.orderId)),
        queryKey: ["verify-transfer"],
        enabled: false,
        refetchInterval: 120000, // 120000 milliseconds = 2 minutes
        onSuccess: (data) => {
            if (data?.status) {
                try {
                    whatsappNotification(transferData?.orderId as string).then((data) => {
                        router?.push(data?.message)
                    })
                } catch (error) {
                    const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                    openErrorModalWithMessage(errorMessage);
                }
                // router.push(`${response?.payment_details?.payment_link}`)
                // toast.success(data?.data?.message, { id: "transfer-success" })
                setOpen(false)
            } else {
                setTransferMsg(data?.message)
            }
        },
        onError: (error) => {
            const errorMessage = formatAxiosErrorMessage(error as AxiosError);
            openErrorModalWithMessage(errorMessage);
        }
    }
    );

    useEffect(() => {
        // Start the interval when the component mounts
        refetch();
    }, [refetch]);


    return (
        <div className='bg-yellow-900 w-full'>
            <Dialog.Root open={open}>
                <Dialog.Portal>
                    <Dialog.Overlay className="fixed  inset-0 z-[99999999999999] bg-[#efefef] backdrop-blur-2xl  transition-all duration-100 data-[state=closed]:animate-out data-[state=closed]:fade-out data-[state=open]:fade-in" />
                    <h2>in</h2>
                    <Dialog.Content className="w-[38.8125rem] z-[999999999999999999] max-w-[98%] max-md:max-h-[90vh] lg:max-w-[71.4375rem] fixed left-[50%] max-md:bottom-0  md:top-[50%] animate-in md:translate-y-[-50%] focus:outline-none data-[state=open]:fade-in-90 data-[state=open]:slide-in-from-bottom-10">

                        <div className="md:mb-5 mb-1 flex items-start justify-start w-full   translate-x-[-50%]">
                            <button className='flex items-center font-bold text-sm md:text-base' onClick={() => setOpen(false)}><svg fill="none" height="20" viewBox="0 0 23 20" width="23" xmlns="http://www.w3.org/2000/svg">
                                <path d="M7.50039 17.225C7.34206 17.225 7.18372 17.1667 7.05872 17.0417L1.62539 11.6084C0.742057 10.725 0.742057 9.27502 1.62539 8.39168L7.05872 2.95835C7.30039 2.71668 7.70039 2.71668 7.94206 2.95835C8.18372 3.20002 8.18372 3.60002 7.94206 3.84168L2.50872 9.27502C2.10872 9.67502 2.10872 10.325 2.50872 10.725L7.94206 16.1583C8.18372 16.4 8.18372 16.8 7.94206 17.0417C7.81706 17.1584 7.65872 17.225 7.50039 17.225Z" fill="#292D32" />
                                <path d="M15.5004 17.225C15.3421 17.225 15.1837 17.1667 15.0587 17.0417L9.62539 11.6084C8.74206 10.725 8.74206 9.27502 9.62539 8.39168L15.0587 2.95835C15.3004 2.71668 15.7004 2.71668 15.9421 2.95835C16.1837 3.20002 16.1837 3.60002 15.9421 3.84168L10.5087 9.27502C10.1087 9.67502 10.1087 10.325 10.5087 10.725L15.9421 16.1583C16.1837 16.4 16.1837 16.8 15.9421 17.0417C15.8171 17.1584 15.6587 17.225 15.5004 17.225Z" fill="#292D32" />
                            </svg>

                                Back to checkout</button>

                        </div>



                        <div className="bg-white  shadow-[hsl(206_22%_7%_/_35%)_0px_10px_38px_-10px,_hsl(206_22%_7%_/_20%)_0px_10px_20px_-15px] md:mb-[3rem] transferModal    rounded-[1.125rem] py-10 px-6 sm:px-12  max-h-[90vh] lg:h-[85vh] translate-x-[-50%]  ">
                            <div className="w-full max-md:overflow-auto max-h-[60vh] max-md:mb-[2rem]">
                                <div className="flex justify-between items-center">
                                    <div className="flex gap-[.325rem]   items-center cursor-pointer">
                                        <div className=" block h-[30px] w-[30px]">
                                            <Image alt='store-logo' height={30} src={`/images/web-store/storeLogo.png`} width={30} />
                                        </div>
                                        <div>
                                            <h2 className='font-semibold text-[8px] md:text-[.8194rem] text-black' style={{ wordBreak: "break-word" }}>{transferData?.company_name}</h2>
                                            <p className='font-medium w-[80%] md:w-auto text-black text-[7px] md:text-[.4919rem]' style={{ wordBreak: "break-word" }}></p>
                                        </div>
                                    </div>
                                    <div className=""><p className=' text-xs md:text-base font-semibold'>{transferData?.contact_phone_number}</p></div>
                                </div>
                                <div className="bg-[#E6E9F24D] border border-[#E6E9F24D] rounded-[.75rem] px-10 py-4 mt-2 md:mt-5 w-full">
                                    <p className='text-sm md:text-lg font-medium'>Amount to pay</p>
                                    <h2 className='text-lg sm:text-[2.625rem] py-2 font-bold'>₦{transferData?.amount} </h2>
                                    <div className="w-full border-t  border-[#E4E7E9]">
                                        <div className="flex items-center gap-[1.125rem] mt-5">
                                            <p className='text-sm md:text-lg text-[#596072]'>Amount <span className='text-base font-bold text-[#191C1F]'>{transferData?.amount}</span></p>
                                            <p className='text-sm md:text-lg text-[#596072]'>Fee <span className='text-base font-bold text-[#191C1F]'>00.00</span></p>
                                        </div>
                                    </div>
                                </div>
                                <div className="mt-4">
                                    <p className='text-sm md:text-lg font-medium text-[#191C1F]'>Transfer to account details below</p>
                                </div>
                                <div className="mt-4 bg-[#FFF5E6] w-full py-3 px-6 rounded-xl border-[.0375rem] border-[#FCB954]">
                                    <p className='text-[#FB9700] text-xs sm:text-base z-40 sm:w-[90%]'>Please do not save the account number, the account is for  a one-time use</p>
                                </div>
                                <div className="mt-6 bg-[#E6E9F24D] py-[1.5rem] w-full flex-col flex justify-center items-center px-[3.125rem] border-[#E6E9F24D] border-[.125rem] rounded-xl">
                                    <p className='text-sm md:text-lg font-medium'>Account details</p>
                                    <h2 className='font-bold text-[1.5rem] md:text-[2.625rem] text-[#191C1F]'>{transferData?.account_number}</h2>
                                    <p className='text-sm md:text-xl font-semibold'>{transferData?.bank_name}</p>
                                    <div className="mt-4 text-center w-full">
                                        <p className='text-sm md:text-lg font-medium'>Account name</p>
                                        <h2 className='text-xs sm:text-[1.375rem] font-semibold'>{transferData?.account_name} </h2>
                                        <div className="mt-4 text-center w-full">
                                            <div className="h-[3px] w-full rounded bg-[#E6E7EA]" />
                                            <p className=' text-[10px] md:text-sm mt-4 flex items-center gap-2'>Account expires in <span><Countdown /></span></p>
                                        </div>
                                    </div>

                                </div>

                            </div>
                            <div className="max-md:fixed max-sm:bg-white sm:relative max-sm:flex justify-center  w-full  items-center border-none outline-none   max-md:h-[6rem] max-sm:px-6 inset-x-0 bottom-0 md:mt-[6rem]">
                                {/* <button className={`px-2 bg-primary h-[3.5rem] w-full text-white rounded-lg font-semibold`} onClick={() => refetch()}>I have made the transfer</button> */}

                                <div className="max-md:fixed max-sm:bg-white sm:relative max-sm:flex justify-center  w-full  items-center border-none outline-none   max-md:h-24 max-sm:px-6 inset-x-0 bottom-0 md:mt-24">
                                    <Button className={`px-2 bg-primary flex items-center justify-center gap-x-3  h-14 w-full text-white rounded-lg font-semibold`} onClick={() => {
                                        refetch().then(() => {
                                            // Show confirmation modal only when button is clicked
                                            if (transferMsg) {
                                                setShowConfirmModal(true);
                                            }
                                        });
                                    }}>I have made the transfer {isFetching && <SmallSpinner color='#fff' />}</Button>
                                </div>
                            </div>
                        </div>
                    </Dialog.Content>
                </Dialog.Portal>
            </Dialog.Root>
            <ErrorModal
                isErrorModalOpen={isErrorModalOpen}
                setErrorModalState={setErrorModalState}
                subheading={
                    errorModalMessage || 'Please check your inputs and try again.'
                }
            >
                <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                    <Button
                        className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
                        size="lg"
                        type="button"
                        onClick={closeErrorModal}
                    >
                        Okay
                    </Button>
                </div>
            </ErrorModal>

            {showConfirmModal && <TranserConfirmModal
                message={transferMsg}
                open={showConfirmModal}
                setOpen={setShowConfirmModal}
            />}
        </div>
    );
};

export default TranserModal;
