import axios from "axios";
// import { contactInfoPropTypes } from "../../checkout/BillingDetails";
import { CartItem } from "../../store/cartStore";
import { useMutation } from "@tanstack/react-query";


export interface CheckoutName {

    // branch?: string;
    company: string;
    web_store: string;
    name: string;
    phone_number: string;
    phone?: string;
    products: CartItem[];


}
export const anonymusUser = async ({ name, phone_number, products, company, web_store }: CheckoutName) => {
    const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_STOCKS_BASE_URL}orders/incomplete_order_record/`, {
        company,
        web_store,
        name,
        phone: 
        phone_number,
        products,



    }
    );
    return response?.data;
};

export const useAnonymusUser = () => {
    return useMutation({
        mutationFn: anonymusUser
    })
}
