import { getServerSession, type NextAuthOptions } from 'next-auth';

import { redirect } from 'next/navigation';

/**
 * Gets the server session or redirects to the login page.
 * @param options - The options object.
 * @param options.authOptions - The authentication options for next-auth.
 * @param options.callbackUrl - The URL to redirect to after login. Defaults to '/dashboard'.
 * @returns The server session object or undefined if redirected.
 */
export async function getServerSessionOrRedirect(options: {
  authOptions: NextAuthOptions;
  callbackUrl?: string;
}) {
  const session = await getServerSession(options.authOptions);


  if (!session) {
    redirect(`/login?callbackUrl=${options.callbackUrl ?? '/dashboard'}`);
  }

  return session;
}

export const THROW_OUT_STATUS_CODES = [100];

/**
 * Throws an unauthorized user out by redirecting them to the login page.
 *
 * @param options - Object containing the status code and an optional callback URL.
 * @param options.statusCode - The HTTP status code.
 * @param options.callbackUrl - Optional callback URL to redirect the user after successful login. Defaults to '/dashboard'.
 */
export async function throwUnauthorizedUserOut(options: {
  statusCode: number | undefined;
  callbackUrl?: string;
}) {
  if (
    !!options.statusCode &&
    THROW_OUT_STATUS_CODES.includes(options.statusCode)
  ) {
    redirect(`/login?callbackUrl=${options.callbackUrl ?? '/dashboard'}`);
  }
}
