// components/Countdown.js
"use client"
// components/Countdown.js
import { useState, useEffect } from 'react';

const Countdown = () => {
    const [timeLeft, setTimeLeft] = useState(37 * 60 + 51);

    useEffect(() => {
        const intervalId = setInterval(() => {
            setTimeLeft(prevTime => (prevTime > 0 ? prevTime - 1 : 0));
        }, 1000);

        return () => clearInterval(intervalId);
    }, []);

    const formatTime = (seconds: number) => {
        const minutes = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${minutes}mins ${secs.toString().padStart(1, '0')}secs`;
    };


    return (
        <div>
            <div>{formatTime(timeLeft)}</div>
            {timeLeft === 0 && <div>Account has expired!</div>}
        </div>
    );
};

export default Countdown;
