export interface checkSuccessProp {
    checkout_order: Checkoutorder;
    payment_details: Paymentdetails;
    buyer_details: Buyerdetails;
}

interface Paymentdetails {
    status: boolean;
    details: string;
    payment_link: string;
    amount: string,
    bank_name: string;
    bank_code: number;
    account_name: string;
    account_number: string;
}





// interface RootObject {
//   checkout_order: Checkoutorder;
//   payment_details: Paymentdetails;
// }


export interface Buyerdetails {
    id: string;
    created_at: string;
    updated_at: string;
    first_name: string;
    middle_name: string;
    last_name: string;
    country: string;
    city: string;
    state: string;
    email: string;
    phone_number: string;
    address: string;
    postal_code: null;
    status: string;
    ship_to_different_address: boolean;
}

export interface Checkoutorder {
    id: string;
    order_id: string;
    company: string;
    company_id: string;
    branch: string;
    branch_id: string;
    buyer: string;
    date: string;
    time: string;
    status: string;
    payment_status: string;
    shipping: number;
    discount: number;
    tax: number;
    total_price: number;
}