import axios from "axios";

export interface storeType {
    company: string;
    branch: string;
    store_brand_color: string;
    store_description: string;
    header_description: string;
    header_banner_type: string;
    banner_color: string;
    header_images: Headerimages;
    header_logos: Headerimages;
    header_logo_text: string;
    navigation_visible: boolean;
    navigation_set_menu: Category[];
    navigation_alignment: string;
    logo_alignment: string;
    contact_visible: boolean;
    contact_phone_number: string;
    contact_email: string;
    contact_address: string;
    contact_description: string;
    redirect_phone_number: string;
    interface_theme: themeProp;
    order_completion_message: string;
    redirect_after_payment_url: string;
    success_message: string;
    store_url: string;
    notification: string;
    company_id: string;
    store_id: string;
    whatsapp_phone_number: string;
    x_link: string;
    facebook_link: string;
    instagram_link: string;
    whatsapp_url: string;
}
export interface Category {
    id: string;
    created_at: string;
    name: string;
    company: null | string;
    created_by: string;
    company_name: string;
    has_products: boolean;
    stock_count: number;
}

export interface Headerimages {
    id: string;
    url: string;
}
export type themeProp = "LIGHT" | "DARK" | "SYSTEM"

export const fetchWebStore = async (url: string) => {
    const { data } = await axios.get(
        `${process.env.NEXT_PUBLIC_API_STOCKS_BASE_URL}instant_web/retrieve_instant_web_by_url/?instant_web_url=${url}`
    );
    return data as storeType;
};


// {{instant_web_local_url}}instant_web/retrieve_instant_web_by_url/?instant_web_url=teeshineyoureyes   