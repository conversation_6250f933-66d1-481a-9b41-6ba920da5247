import React from 'react'

const ProductEmptyState = () => {
    return (
        <div className='bg-white w-full h-[31.25rem] rounded-[20px] gap-6  flex justify-center items-center flex-col'>
            <svg fill="none" height="97" viewBox="0 0 97 97" width="97" xmlns="http://www.w3.org/2000/svg">
                <circle cx="48.5" cy="48.5" fill="#EFEFEF" r="48.5" />
                <path d="M41.2913 24.833L32.543 33.6055" stroke="#292D32" strokeLinecap="round" strokeLinejoin="round" strokeMiterlimit="10" strokeWidth="1.5" />
                <path d="M56.709 24.833L65.4573 33.6055" stroke="#292D32" strokeLinecap="round" strokeLinejoin="round" strokeMiterlimit="10" strokeWidth="1.5" />
                <path d="M24.834 38.971C24.834 34.5002 27.2265 34.1377 30.199 34.1377H67.8023C70.7748 34.1377 73.1673 34.5002 73.1673 38.971C73.1673 44.1669 70.7748 43.8044 67.8023 43.8044H30.199C27.2265 43.8044 24.834 44.1669 24.834 38.971Z" stroke="#292D32" strokeWidth="1.5" />
                <path d="M43.5859 53.833V62.4122" stroke="#292D32" strokeLinecap="round" strokeWidth="1.5" />
                <path d="M54.7031 53.833V62.4122" stroke="#292D32" strokeLinecap="round" strokeWidth="1.5" />
                <path d="M28.459 44.167L31.8665 65.047C32.6398 69.7353 34.5007 73.167 41.4123 73.167H55.9848C63.5007 73.167 64.6123 69.8803 65.4823 65.337L69.5423 44.167" stroke="#292D32" strokeLinecap="round" strokeWidth="1.5" />
            </svg>

            <p className='text-base text-[#4E4E4E] font-medium'>Product is currently out of stock</p>
        </div>
    )
}

export default ProductEmptyState