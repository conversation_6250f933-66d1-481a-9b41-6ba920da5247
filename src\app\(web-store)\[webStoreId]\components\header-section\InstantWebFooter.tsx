// import { imgProp } from '@/app/(dashboard)/instant-web/misc/store';
import Image from 'next/image';
import React from 'react'
import CallIcon from '../icons/CallIcon';
import EmailIcon from '../icons/EmailIcon';
import LocationIcon from '../icons/LocationIcon';
import { Headerimages } from '../api/store/fetchStore';
interface Prop {
    header_description: string;
    header_images: Headerimages;
    header_logos: Headerimages;
    header_logo_text: string;
    contact_phone_number: string
    contact_email: string
    contact_address: string
    contact_description: string
    darkMode: string | boolean
}

const InstantWebFooter = ({ header_description, darkMode, header_logos, header_logo_text, contact_address, contact_description, contact_email, contact_phone_number }: Prop) => {
    return (
        <div className={`w-full mt-6 ${darkMode ? "dark" : ""}`}>
            <div className="border-y w-full py-12 border-opacity-75 flex flex-col justify-center items-center" >
                <div className="flex gap-[12.3696px] items-center cursor-pointer">
                    {header_logos?.url && <div className="hidden @md:block h-[48px] w-[48px]">
                        <Image alt='store-logo' height={48} src={header_logos?.url} width={48} />
                    </div>}
                    <div>
                        <h2 className='font-semibold text-sm text-white md:text-black md:text-[22px] dark:text-white' style={{ wordBreak: "break-word" }}>{header_logo_text}</h2>
                        <p className='font-medium w-[95%] md:w-auto text-white md:text-black text-[10px] md:text-xs dark:text-white' style={{ wordBreak: "break-word" }}>{header_description}</p>
                    </div>
                </div>
                {
                    contact_description && <div className="mt-4 max-w-3xl text-center">
                        <p>{contact_description}</p>
                    </div>
                }
                <div className="flex items-center mt-8 justify-center flex-wrap divide-x-2 divide-opacity-60 ">
                    {contact_phone_number && <div className="pr-4">
                        <div className='font-bold flex items-center text-base gap-1 text-[#394157] dark:text-white flex-wrap'><CallIcon color={darkMode ? "#fff" : "#000"} /> : <p className='font-medium text-sm text-opacity-90'>{contact_phone_number}</p> </div>
                    </div>}
                    {contact_email && <div className="px-4">

                        <div className='font-bold flex items-center text-base gap-1 text-[#394157] flex-wrap dark:text-white'><EmailIcon color={darkMode ? "#fff" : "#000"} />: <p className='font-medium text-sm text-opacity-90'>{contact_email}</p> </div>

                    </div>}
                    {contact_address && <div className="px-4">
                        <div className='font-bold flex items-center text-base gap-1 text-[#394157] flex-wrap dark:text-white'><LocationIcon color={darkMode ? "#fff" : "#000"} />: <p className='font-medium text-sm text-opacity-90'>{contact_address}</p> </div>

                    </div>}
                </div>
            </div>

        </div >
    )
}

export default InstantWebFooter