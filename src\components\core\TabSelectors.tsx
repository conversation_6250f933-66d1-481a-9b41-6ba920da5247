'use client';

import { Tab } from '@headlessui/react';
import clsx from 'clsx';
import * as React from 'react';

interface TabSelectorsProps {
  selectorHeadings: string[];
}

export const TabSelectors: React.FunctionComponent<TabSelectorsProps> = ({
  selectorHeadings,
}) => {
  return (
    <Tab.List className="my-0 mt-4 flex space-x-1 rounded-[10px] bg-[#fff] px-1 pt-2">
      {selectorHeadings.map(h => {
        return (
          <Tab
            className={({ selected }) =>
              clsx(
                'ring-opacity/60 w-full py-2.5 text-sm font-medium leading-5 text-[#4E4E4E] ring-white md:w-[25%]',
                selected
                  ? '!border-b-[3px] border-[#107EE2] font-semibold !text-[#107EE2]'
                  : 'text-[#4E4E4E]'
              )
            }
            key={h}
          >
            {h}
          </Tab>
        );
      })}
    </Tab.List>
  );
};
