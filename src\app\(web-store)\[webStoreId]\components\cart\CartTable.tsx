



'use client'
import React from 'react'
import Image from 'next/image'
import { createC<PERSON>umnHelper, flexRender, getCoreRowModel, getFilteredRowModel, getPaginationRowModel, useReactTable } from '@tanstack/react-table'
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,

} from "@/components/core/Table"
import AddIcon from '../icons/AddIcon';
import MinusIcon from '../icons/MinusIcon';
import useCartStore, { CartItem } from '../store/cartStore';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment


interface DataListHeader {
    id?: string;
    product_img?: string;
    product_name?: string;
    product_description?: string;
    price: number;
    quantity: number;
    subTotal: number
}

interface Prop {
    // setData: React.Dispatch<React.SetStateAction<{
    //     id: string;
    //     company_id: string,
    //     branch_id: string;
    //     product_img: string;
    //     product_name: string;
    //     product_description: string;
    //     price: number;
    //     quantity: number;
    //     subTotal: number;
    // }[]>>
    setData: React.Dispatch<React.SetStateAction<CartItem[]>>
    data: {
        id: string;
        branch_id: string;
        company_id: string,
        product_img: string;
        product_name: string;
        product_description: string;
        price: number;
        quantity: number;
        subTotal: number;
    }[]
    storeId: string;
}

const CartTable = ({ setData, data, storeId }: Prop) => {
    // const [data, setData] = useState(CartData);
    const deleteFromCart = useCartStore((state) => state?.deleteFromCart)
    const updateCart = useCartStore((state) => state?.updateCart)
    const cart = useCartStore((state) => state.carts[storeId]);

    const handleQuantityChange = (cartId: string, rowIndex: number, increment: number) => {
        const currentCartItem = cart?.find(item => item?.id === cartId);
        if (!currentCartItem) return;

        const newQuantity = Math.max(0, currentCartItem?.quantity + increment); // Ensure quantity doesn't drop below 0

        if (newQuantity <= 0) {

            deleteFromCart(storeId, cartId); // Remove item from cart if quantity reaches 0
            // setData(data?.filter((prod => String(prod?.id) !== String(cartId))))
        } else {
            updateCart(storeId, cartId, newQuantity);
        }


        setData((prevData) =>
            prevData?.map((item, index) =>
                index === rowIndex
                    ? {
                        ...item,
                        quantity: Math.max(0, item?.quantity + increment), // Prevents quantity from going below 1
                        subTotal: item.price * Math.max(1, item?.quantity + increment),
                    }
                    : item
            )
        );

    };




    const handleRemoveCart = (id: string) => {
        // setData(data?.filter((prod => String(prod?.id) !== String(id))))
        deleteFromCart(storeId, id); // Remove item from cart 
    }

    const columnHelper = createColumnHelper<DataListHeader>()

    const column = [
        columnHelper?.accessor('product_name', {
            header: () => 'Products',
            cell: (info) => (
                <div className='flex items-center gap-1'>
                    <div className='remove-icon-wrapper '>
                        <svg className='icon' fill='none' height='20' viewBox='0 0 24 24' width='20' xmlns='http://www.w3.org/2000/svg' onClick={() => handleRemoveCart(String(info?.row?.original?.id))}>
                            <path
                                d='M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z'
                                stroke='#929FA5'
                                strokeMiterlimit='10'
                                strokeWidth='1.5'
                            />
                            <path d='M15 9L9 15' stroke='#929FA5' strokeLinecap='round' strokeLinejoin='round' strokeWidth='1.5' />
                            <path d='M15 15L9 9' stroke='#929FA5' strokeLinecap='round' strokeLinejoin='round' strokeWidth='1.5' />
                        </svg>
                    </div>
                    <div className='w-[3rem] h-[2rem] md:h-[4rem] relative'>
                        <Image
                            alt={info?.row?.original?.product_name}
                            src={info?.row?.original?.product_img}
                            fill
                            style={{ objectFit: "contain" }}
                            sizes="48px"
                        />
                    </div>
                    <p style={{ wordBreak: 'break-word' }}>{info?.getValue()}</p>
                </div>
            ),
        }),
        columnHelper?.accessor('price', {
            header: () => 'Price',
            cell: (info) => `₦${info?.getValue()}`,
        }),
        columnHelper?.accessor('quantity', {
            header: () => 'Quantity',
            cell: (info) => (
                <div className='flex border border-[#E4E7E9] rounded items-center p-2 md:p-4 gap-[.2813rem]'>
                    <button className='p-1 ' onClick={() => handleQuantityChange(String(info?.row?.original?.id), info.row.index, -1)}>
                        <MinusIcon className='w-[.75rem] md:w-[1rem]' />
                    </button>
                    <input
                        className=' w-[1.5rem] md:max-w-[3rem] bg-transparent outline-none border-none text-center text-sm md:text-base text-black'
                        type='number'
                        value={info?.getValue()}
                        readOnly
                    />
                    <button className='p-1 ' onClick={() => handleQuantityChange(String(info?.row?.original?.id), info.row.index, 1)}>
                        <AddIcon className='w-[.75rem] md:w-[1rem]' />
                    </button>
                </div>
            ),
        }),
        columnHelper?.accessor('subTotal', {
            header: () => 'Sub-total',
            cell: (info) => `₦${info?.getValue()}`,
        }),
    ];

    // const rows = useMemo(() => data?.data?.data ?? [], [data?.data?.data]);
    let loading;

    // const [data] = useState(mockRequstData)
    const table = useReactTable({
        data,
        columns: column,
        debugTable: true,

        getFilteredRowModel: getFilteredRowModel(),
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel()

    })

    // Filter data based on current page and page size

    return (


        <div className='w-full '>

            {loading ? ( // Render skeleton loading if loading state is true
                <>
                    <p>loading</p>
                </>
            ) : ( // Render table if loading state is false
                <Table>
                    <TableHeader className='bg-[#F2F4F5]'>
                        {
                            table?.getHeaderGroups()?.map((headerGroup) => (
                                <TableRow key={headerGroup?.id}>
                                    {
                                        headerGroup?.headers?.map((header) => (
                                            <TableHead className='font-nunito text-sm text-[#030229] ' key={header?.id}>
                                                {
                                                    flexRender(
                                                        header?.column?.columnDef?.header, header?.getContext()
                                                    )
                                                }
                                            </TableHead>
                                        ))
                                    }
                                </TableRow>
                            ))
                        }
                    </TableHeader>

                    <>
                        {data?.length > 0

                            ?
                            <TableBody className=''>
                                {


                                    table?.getRowModel()?.rows?.map((row) => (
                                        <React.Fragment key={row?.id}>

                                            <TableRow className='hover:bg-[#f5f7ff] ' key={row?.id} >
                                                {row?.getVisibleCells()?.map((cell) => (
                                                    <TableCell className='text-xs cursor-pointer font-nunito dark:text-[#E0E4E6]' key={cell?.id} >
                                                        {flexRender(cell?.column?.columnDef?.cell, cell?.getContext())}
                                                    </TableCell>
                                                ))}
                                            </TableRow>

                                        </React.Fragment>
                                    ))
                                }
                            </TableBody>

                            :
                            <div className='w-ful flex justify-center items-center  text-sm p-5 dark:text-white' >

                                No data found


                            </div>

                        }

                    </>
                </Table>
            )}


        </div>


    )
}

export default CartTable
