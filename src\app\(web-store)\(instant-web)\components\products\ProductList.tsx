import React, { useEffect, useState } from 'react';
import axios from 'axios';
import Image from 'next/image';
import { useInfiniteQuery } from '@tanstack/react-query';
import useInfiniteScroll from 'react-infinite-scroll-hook';
import ProductDetails from './ProductDetails';
import { StockProps, ProductTpes } from '@/app/(web-store)/[webStoreId]/components/types/products/producttype';
import { Spinner } from '@/icons/core';
import useCartStore, { CartItem } from '@/app/(web-store)/[webStoreId]/components/store/cartStore';
import { CartData } from '@/app/(web-store)/[webStoreId]/components/mock/cartData';
import { Button } from '@/components/core';
import { addCommasToNumber } from '@/utils/numbers';
import MinusIcon from '@/app/(web-store)/[webStoreId]/components/icons/MinusIcon';
import AddIcon from '@/app/(web-store)/[webStoreId]/components/icons/AddIcon';
import CheckoutDetails from '../checkout/CheckoutDetails';
import ProductEmptyState from './ProductEmptyState';
import LoadMoreicon from '../icons/LoadMoreIcon';
import { convertKebabAndSnakeToTitleCase } from '@/utils/strings';

interface Prop {
    activeTab: string;
    globalFilter: string;
    id: string;
    storeId: string;
    setShowSuccessModal: React.Dispatch<React.SetStateAction<boolean>>
    setOrderId: React.Dispatch<React.SetStateAction<string>>;
    CheckoutDetail: {
        first_nmae: string;
        last_name: string;
        phone_number: string;
    }
}
interface filteKey {
    activeTab: string;
    globalFilter: string;
    id: string;
    pageParam: number;
    size: number;
}
const ProductList: React.FC<Prop> = ({ activeTab, globalFilter, id, storeId, setShowSuccessModal, setOrderId, CheckoutDetail }) => {
    const size = 5; // Define number of products per page
    const [open, setOpen] = useState(false);
    const [selectedProduct, setSelectedProduct] = useState<StockProps | undefined>();

    const fetchProducts = async ({ id, globalFilter, activeTab, pageParam = 1, size }: filteKey) => {
        const { data } = await axios.get(
            `${process.env.NEXT_PUBLIC_API_STOCKS_BASE_URL}instant_web/company_available_stock?company_id=${id}&category_id=${activeTab}&search=${globalFilter}&page=${pageParam}&size=${size}`
        );
        return data as ProductTpes;
    };

    const {
        data,
        error,
        isLoading,
        // isFetching,
        fetchNextPage,
        hasNextPage,
        isFetchingNextPage,
    } = useInfiniteQuery(
        ['fetch-products', id, globalFilter, activeTab],
        ({ pageParam = 1 }) => fetchProducts({ id, globalFilter, activeTab, pageParam, size }),
        {
            staleTime: 6000,
            enabled: !!id,
            getNextPageParam: (lastPage, pages) => {
                if (lastPage?.next !== null) {
                    return pages.length + 1;
                } else {
                    return;
                }
            }
        }
    );

    const [sentryRef] = useInfiniteScroll({
        loading: isFetchingNextPage || isLoading,
        hasNextPage: !!hasNextPage,
        onLoadMore: fetchNextPage,
        disabled: !!error,
        rootMargin: '0px 0px 400px 0px',
    });


    const products = data?.pages.flatMap((page) => page.results) || [];
    const addToCart = useCartStore(state => state.addToCart);
    const updateCart = useCartStore(state => state.updateCart);
    const deleteFromCart = useCartStore(state => state.deleteFromCart);
    const cart = useCartStore((state) => state.carts[storeId]);
    const [cartDataArray, setCartDataArray] = useState<CartItem[]>(CartData);
    const [showCheckoutModal, setShowCheckoutModal] = useState(false)
    useEffect(() => {
        setCartDataArray(cart);
    }, [cart]);

    // const isInCart = cartDataArray?.some(cart => cart.id === productId);

    const addItemToCart = (productItem: StockProps) => {
        if (!productItem) return;

        const newCartItem = {
            id: productItem?.item,
            branch_id: String(productItem?.branch),
            company_id: id,
            product_img: productItem?.image,
            product_name: productItem?.item_name,
            product_description: productItem?.product_description,
            price: Number(productItem.selling_price),
            quantity: 1,
            subTotal: Number(productItem.selling_price),
        };

        addToCart(storeId, [newCartItem]);
    };



    const handleQuantityChange = (productId: string, change: number) => {
        const currentCartItem = cart?.find(item => item?.id === productId);
        if (!currentCartItem) return;

        const newQuantity = Math.max(0, currentCartItem?.quantity + change); // Ensure quantity doesn't drop below 0

        if (newQuantity <= 0) {
            deleteFromCart(storeId, productId); // Remove item from cart if quantity reaches 0
        } else {
            updateCart(storeId, productId, newQuantity);
        }
    };



    return (
        <div className='relative overflow-x-auto'>
            {isLoading ? (
                <div
                    className=" flex w-full py-7 justify-center items-center"
                >
                    <Spinner color='blue' />
                </div>
            ) : error ? (
                <div className="py-10 rounded-10 text-center my-6 w-full bg-white"> <p className='text-red-500 font-outfit text-sm font-semibold'>Error fetching products</p></div>
            ) : (
                <div className=' overflow-y-auto  w-full'>
                    {products?.length > 0 ?
                        <div className="grid grid-cols-3 md:grid-cols-3 xl:grid-cols-4  2xl:grid-cols-5 gap-2 sm:gap-4 w-[43rem] sm:w-[55rem] overflow-x-auto   lg:w-full">
                            {products?.map((product: StockProps, idx: number) => (
                                <div className="bg-white p-4 hover:shadow-lg border rounded-[1.25rem] cursor-pointer" key={product?.item + idx} >
                                    <div className="h-[9rem] sm:h-[11.5rem] relative bg-[#f4f3f1] flex justify-center items-center" onClick={() => {
                                        setOpen(true);
                                        setSelectedProduct(product);
                                    }}>
                                        {product?.image ? (
                                            <Image
                                                alt={product?.item_name || 'Product image'}
                                                className='object-cover rounded-[1.25rem]'
                                                layout='fill'
                                                src={product?.image}
                                            />
                                        ) : <div></div>}
                                    </div>
                                    <div className="mt-[.625rem]" onClick={() => {
                                        setOpen(true);
                                        setSelectedProduct(product);
                                    }}>
                                        <p className='text-black text-xs lg:text-[.9375rem] font-semibold font-sans truncate'>{convertKebabAndSnakeToTitleCase(product?.item_name)}</p>
                                        <p className='text-black text-xs font-sans py-1 truncate'>{convertKebabAndSnakeToTitleCase(product?.product_description)}</p>
                                    </div>
                                    <div className="flex items-center justify-between mt-1 py-1">
                                        <p className='text-black text-xs xl:text-base font-semibold font-sans'>&#8358;{addCommasToNumber(Number(product?.selling_price))}</p>
                                        {!cartDataArray?.some(cart => cart.id === product?.item) ? (
                                            <>
                                                {/* <Button className='bg-black h-[2.375rem] md:hidden rounded-[.625rem]' onClick={() => addItemToCart(product)}>Add  </Button> */}
                                                <Button className='bg-black h-[2.375rem] px-4 md:px-4 xl:px-6 block rounded-[.625rem]' onClick={() => addItemToCart(product)}>Add to cart</Button>
                                            </>

                                        ) : (
                                            <div className='flex border bg-black rounded-[.625rem] h-[2.5rem]  items-center p-2 md:px-4 md:py-2 gap-x-[.2813rem]'>
                                                <Button className='p-2  bg-[#323232] w-[1.875rem]  h-[1.625rem] rounded-lg flex justify-center items-center' onClick={() => handleQuantityChange(String(product?.item), -1)}>
                                                    <MinusIcon color='white' />
                                                </Button>
                                                <input
                                                    className='max-w-[1.8rem] md:max-w-[1.5rem] h-[2.375rem]  bg-transparent outline-none border-none text-center text-base text-white dark:text-white'
                                                    type='number'
                                                    value={cartDataArray?.find(cart => cart?.id === product?.item)?.quantity}
                                                    readOnly
                                                />
                                                <Button className='p-1    bg-[#323232] w-[1.875rem]  h-[1.625rem] rounded-lg flex justify-center items-center' onClick={() => handleQuantityChange(String(product?.item), 1)}>
                                                    <AddIcon className='' color='white' size={10} />
                                                </Button>
                                            </div>
                                        )}
                                    </div>

                                </div>
                            ))}
                        </div>
                        : <ProductEmptyState />}





                    <div
                        className=" flex w-full py-7  border justify-center items-center"
                        ref={sentryRef}
                    >
                        {isFetchingNextPage && <LoadMoreicon />}
                    </div>
                </div>
            )
            }

            {open && <ProductDetails id={id} open={open} product={selectedProduct} setOpen={setOpen} setShowCheckoutModal={setShowCheckoutModal} showCheckoutModal={showCheckoutModal} storeId={storeId} />}
            {
                showCheckoutModal && <CheckoutDetails
                    CheckoutDetails={CheckoutDetail}
                    open={showCheckoutModal}
                    setOpen={setShowCheckoutModal}
                    setOrderId={setOrderId}
                    setShowCart={setOpen}
                    setShowSuccessModal={setShowSuccessModal}
                    storeId={storeId}


                />
            }
        </div >
    );
};

export default ProductList;
