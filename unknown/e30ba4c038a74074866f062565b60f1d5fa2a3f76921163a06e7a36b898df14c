import React from 'react'
import { Toolt<PERSON>Provider, <PERSON><PERSON><PERSON>, Toolt<PERSON>Trigger, TooltipContent } from './ToolTipPrimitives'
import { cn } from '@/utils/classNames'

interface ToolTipProps {
    content: string | React.ReactNode
    align?: "center" | "start" | "end"
    className?: string
    children: React.ReactNode
    contentClass?: string
    asChild?: boolean
}

const ToolTip: React.FC<ToolTipProps> = ({ content, children, className, align, contentClass, asChild = false }) => {
    return (
        <TooltipProvider>
            <Tooltip>
                <TooltipTrigger asChild={asChild} className={className}>
                    {children}
                </TooltipTrigger>
                <TooltipContent align={align} className={cn(contentClass)}>
                    {content}
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>
    )
}
export default ToolTip