export const generateColorArray = () => {
    const color = [
        "#DDFFED", "#FFF7E299", "#F7EAFF99", "#E6F3FF99",
        "#DDFFED", "#FFF7E299", "#F7EAFF99", "#E6F3FF99",
        "#DDFFED", "#FFF7E299", "#F7EAFF99", "#E6F3FF99"
    ];
    const repeatedColorArray = [];

    while (repeatedColorArray.length < 150) {
        repeatedColorArray.push(...color);
    }

    return repeatedColorArray.slice(0, 150);
};
