"use client"
import React from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { useErrorModalState } from '@/hooks';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { AxiosError } from 'axios';
import { Button, ErrorModal, LoaderModal } from '@/components/core';
import { whatsappNotification } from '../[webStoreId]/components/api/checkout/whatsappNotification';

const Page = () => {
    const search = useSearchParams();
    const orderId = search.get("reference")
    const router = useRouter();
    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();

    const { isLoading } = useQuery({
        queryFn: () => whatsappNotification(orderId as string),
        queryKey: ['whatsapp-notification', orderId],
        onSuccess: (data) => {
            router.push(data?.message);
        },
        onError: (error) => {
            const errorMessage = formatAxiosErrorMessage(error as AxiosError);
            openErrorModalWithMessage(errorMessage);
        },
        enabled: !!orderId,
    });

    return (
        <div>ddddddddd
            {isLoading && <LoaderModal />}
            <ErrorModal
                isErrorModalOpen={isErrorModalOpen}
                setErrorModalState={setErrorModalState}
                subheading={errorModalMessage || 'Please check your inputs and try again.'}
            >
                <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                    <Button
                        className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
                        size="lg"
                        type="button"
                        onClick={closeErrorModal}
                    >
                        Okay
                    </Button>
                </div>
            </ErrorModal>

        </div>
    );
};

export default Page;
