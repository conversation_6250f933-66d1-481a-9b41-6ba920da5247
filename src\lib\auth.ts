import { userTokenSchema } from '@/schemas/auth';

import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';

import { z } from 'zod';

import { authFetchClient, spendManagementFetchClient } from './apiClient';

export type UserToken = z.infer<typeof userTokenSchema>;

export type LoginDto = {
  email: string | undefined;
  password: string | undefined;
  device_type: string;
};

export type LoginResponse = {
  refresh: string;
  access: string;
};

export type SpendManagementLoginDto = {
  email: string | undefined;
  password: string | undefined;
};

export type SpendManagementLoginResponse = {
  message: string;
};

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      // The name to display on the sign in form (e.g. 'Sign in with...')
      name: 'Credentials',

      // The credentials is used to generate a suitable form on the sign in page.
      // You can specify whatever fields you are expecting to be submitted.
      // e.g. domain, username, password, 2FA token, etc.
      // You can pass any HTML attribute to the <input> tag through the object.
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      //@ts-ignore
      async authorize(credentials) {
        const { email, password } = credentials || {};

        const data = { email, password, device_type: 'MOBILE' };

        const response = (await authFetchClient<LoginResponse, LoginDto>(
          'user/login/create/',
          {
            method: 'POST',
            body: data,
          }
        )) as LoginResponse;

        /**
         *  This will run only after the actual login is successful.
         *  This is because errors are thrown by the fetch client.
         */
        await spendManagementFetchClient<
          SpendManagementLoginResponse,
          SpendManagementLoginDto
        >('core/sign-in/', {
          method: 'POST',
          body: { email, password },
        });

        return response;
      },
    }),
  ],

  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },

  callbacks: {
    async jwt({ token, user }) {
      return { ...token, ...user };
    },

    async session({ session, token }) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      //@ts-ignore
      session.user = token as UserToken;
      return session;
    },
  },

  pages: {
    signIn: '/login',
  },
};
