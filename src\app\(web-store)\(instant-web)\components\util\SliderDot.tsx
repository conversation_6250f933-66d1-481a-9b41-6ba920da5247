// import React from 'react';
// import Image from 'next/image';

// interface DotPagingProps {
//     images: string[];
//     index: number;
// }

// const DotPaging: React.FC<DotPagingProps> = ({ images, index }) => {
//     // console.log(images, index);

//     return (
//         <div className='relative bg-[#f4f3f1] w-[60px] h-[60px] p-3 rounded-10 flex justify-center items-center'>
//             <div className="w-[50px] h-[50px] inset-0   z-[9999] overflow-hidden  rounded-10">
//                 {images[index] !== null && <Image
//                     alt={`Thumbnail ${index + 1}`}
//                     className="rounded-10 cursor-pointer"
//                     layout="fill"
//                     objectFit="cover"
//                     src={images[index]}
//                 />}

//             </div>
//         </div>
//     );
// };

// export default DotPaging;
import React from 'react';
import Image from 'next/image';

interface DotPagingProps {
    images: string[];
    index: number;
    onClick: (index: number) => void; // Add an onClick handler prop
}

const DotPaging: React.FC<DotPagingProps> = ({ images, index, onClick }) => {
    return (
        <div
            className='relative bg-[#f4f3f1] w-[60px] h-[60px] p-3 rounded-10 flex justify-center items-center cursor-pointer'
            onClick={() => onClick(index)} // Call the handler when clicked
        >
            <div className="w-[50px] h-[50px] inset-0 z-[9999] overflow-hidden rounded-10">
                {images[index] !== null && (
                    <Image
                        alt={`Thumbnail ${index + 1}`}
                        className="rounded-10"
                        layout="fill"
                        objectFit="cover"
                        src={images[index]}
                    />
                )}
            </div>
        </div>
    );
};

export default DotPaging;
